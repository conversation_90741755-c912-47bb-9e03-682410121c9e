{"cells": [{"cell_type": "code", "execution_count": 1, "id": "setup", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5372\\4290915875.py:30: LangChainDeprecationWarning: The class `Neo4jGraph` was deprecated in LangChain 0.3.8 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-neo4j package and should be used instead. To use it run `pip install -U :class:`~langchain-neo4j` and import as `from :class:`~langchain_neo4j import Neo4jGraph``.\n", "  graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Neo4j connection established\n", "🚀 Setup complete! Ready for complete analysis with focused filtering...\n"]}], "source": ["# ========== IMPORTS AND SETUP ==========\n", "import os\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "import re\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# ========== CONFIGURATION ==========\n", "BASE_PATH = Path(r'C:/Shaik/sample/OneInsights')\n", "NEO4J_URI = 'bolt://localhost:7687'\n", "NEO4J_USER = 'neo4j'\n", "NEO4J_PASSWORD = 'Test@7889'\n", "NEO4J_DB = 'oneinsights-v10'\n", "GOOGLE_API_KEY = 'AIzaSyD8qIjUPsRbhJr59qoIIhczbPPKA2hter0'\n", "\n", "# ========== INITIALIZE COMPONENTS ==========\n", "try:\n", "    graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "    print('✅ Neo4j connection established')\n", "except Exception as e:\n", "    print(f'❌ Neo4j connection failed: {e}')\n", "    graph = None\n", "\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "llm = ChatGoogleGenerativeAI(\n", "    model='gemini-2.0-flash-exp',\n", "    temperature=0,\n", "    google_api_key=GOOGLE_API_KEY\n", ")\n", "\n", "# ========== FOCUSED FILTERING (from v9) ==========\n", "NOISE_VARIABLES = {\n", "    # Loop variables\n", "    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',\n", "    # Common temporary variables\n", "    'temp', 'tmp', 'obj', 'item', 'elem', 'node', 'val', 'value',\n", "    # Common short variables\n", "    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',\n", "    # Reserved keywords\n", "    'this', 'super', 'null', 'true', 'false', 'void', 'return',\n", "    # Meaningless short names\n", "    'it', 'ex', 'e1', 'e2', 'o1', 'o2'\n", "}\n", "\n", "APPLICATIONS = {\n", "    'ServiceBolt': 'REST API Service Layer',\n", "    'UnifiedBolt': 'Core Business Logic and Data Layer'\n", "}\n", "\n", "def is_meaningful_variable(var_name):\n", "    '''Filter out noise variables and keep only meaningful ones (v9 focused filtering)'''\n", "    if not var_name or len(var_name) < 2:\n", "        return False\n", "    \n", "    if var_name.lower() in NOISE_VARIABLES:\n", "        return False\n", "    \n", "    # Focus on business-meaningful variables (v9 enhancement)\n", "    if len(var_name) > 4:  # Increased threshold for more meaningful names\n", "        return True\n", "    \n", "    meaningful_short = {'id', 'url', 'api', 'key', 'dto', 'dao', 'req', 'res', 'ctx', 'data'}\n", "    return var_name.lower() in meaningful_short\n", "\n", "def detect_application(file_path):\n", "    '''Detect which application a file belongs to'''\n", "    path_str = str(file_path).replace('\\\\', '/')\n", "    if 'ServiceBolt' in path_str:\n", "        return 'ServiceBolt'\n", "    elif 'UnifiedBolt' in path_str:\n", "        return 'UnifiedBolt'\n", "    return 'Unknown'\n", "\n", "print('🚀 Setup complete! Ready for complete analysis with focused filtering...')"]}, {"cell_type": "code", "execution_count": 2, "id": "stage1_hierarchy", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📁 Stage 1: Extracting enhanced folder and file hierarchy...\n", "✅ Stage 1 Complete: 8 folder relationships, 19 file relationships, 2 applications\n"]}], "source": ["# ========== STAGE 1: ENHANCED FOLDER + FILE HIERARCHY (from v8 + v9 focus) ==========\n", "\n", "def extract_enhanced_folder_file_hierarchy(base_path):\n", "    '''Extract complete hierarchy with v9 focused structure: PROJECT -> APPLICATIONS -> FOLDERS -> FILES'''\n", "    folder_records, file_records, app_records = [], [], []\n", "    base_path = os.path.abspath(base_path)\n", "    base_folder_name = os.path.basename(base_path)\n", "    processed_folders = set()\n", "    detected_apps = set()\n", "\n", "    for root, dirs, files in os.walk(base_path):\n", "        rel_root = os.path.relpath(root, base_path)\n", "        app_name = detect_application(root)\n", "        \n", "        if app_name != 'Unknown':\n", "            detected_apps.add(app_name)\n", "\n", "        # Handle folder hierarchy with proper parent-child relationships\n", "        if rel_root != '.':\n", "            path_parts = rel_root.split(os.sep)\n", "            for i in range(len(path_parts)):\n", "                current_folder = path_parts[i]\n", "                parent = path_parts[i-1] if i > 0 else None\n", "                \n", "                # Determine parent type and name (v9 focused approach)\n", "                if parent is None:\n", "                    # Top-level folder under application\n", "                    if app_name != 'Unknown':\n", "                        parent_name = app_name\n", "                        parent_type = 'application'\n", "                    else:\n", "                        parent_name = base_folder_name\n", "                        parent_type = 'project'\n", "                else:\n", "                    parent_name = parent\n", "                    parent_type = 'folder'\n", "                \n", "                folder_key = f'{parent_name}->{current_folder}'\n", "                if folder_key not in processed_folders:\n", "                    folder_records.append({\n", "                        'source_node': parent_name,\n", "                        'source_type': parent_type,\n", "                        'destination_node': current_folder,\n", "                        'destination_type': 'folder',\n", "                        'relationship': 'contains',\n", "                        'file_path': None,\n", "                        'application': app_name\n", "                    })\n", "                    processed_folders.add(folder_key)\n", "\n", "        # Handle files\n", "        current_folder = os.path.basename(root) if rel_root != '.' else base_folder_name\n", "        for f in files:\n", "            if f.endswith('.java'):\n", "                file_rel_path = os.path.relpath(os.path.join(root, f), base_path)\n", "                file_records.append({\n", "                    'source_node': current_folder,\n", "                    'source_type': 'folder' if rel_root != '.' else 'project',\n", "                    'destination_node': f,\n", "                    'destination_type': 'file',\n", "                    'relationship': 'contains',\n", "                    'file_path': file_rel_path,\n", "                    'application': app_name\n", "                })\n", "    \n", "    # Create PROJECT -> APPLICATION relationships (v9 focus)\n", "    for app in detected_apps:\n", "        app_records.append({\n", "            'source_node': base_folder_name,\n", "            'source_type': 'project',\n", "            'destination_node': app,\n", "            'destination_type': 'application',\n", "            'relationship': 'contains',\n", "            'file_path': None,\n", "            'application': app\n", "        })\n", "    \n", "    return folder_records, file_records, app_records\n", "\n", "print('📁 Stage 1: Extracting enhanced folder and file hierarchy...')\n", "folder_records, file_records, app_records = extract_enhanced_folder_file_hierarchy(BASE_PATH)\n", "df_folders = pd.DataFrame(folder_records)\n", "df_files = pd.DataFrame(file_records)\n", "df_apps = pd.DataFrame(app_records)\n", "\n", "print(f'✅ Stage 1 Complete: {len(df_folders)} folder relationships, {len(df_files)} file relationships, {len(df_apps)} applications')"]}, {"cell_type": "code", "execution_count": 3, "id": "stage2_utility_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 Stage 2: Utility functions loaded\n"]}], "source": ["# ========== STAGE 2: UTILITY FUNCTIONS (from v8) ==========\n", "\n", "def read_source_code(file_path):\n", "    '''Read source code file'''\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        return f.read().encode('utf-8')\n", "\n", "def extract_package_and_imports(source_code_str):\n", "    '''Extract package name and imports from Java source'''\n", "    package_pattern = r'package\\s+([\\w\\.]+);'\n", "    import_pattern = r'import\\s+([\\w\\.]+);'\n", "    package_match = re.search(package_pattern, source_code_str)\n", "    package_name = package_match.group(1) if package_match else None\n", "    import_matches = re.findall(import_pattern, source_code_str)\n", "    return package_name, import_matches\n", "\n", "def extract_api_endpoints(source_code_str):\n", "    '''Extract REST API endpoints from Spring annotations'''\n", "    endpoints = []\n", "    mapping_patterns = {\n", "        'RequestMapping': [\n", "            r'@RequestMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@RequestMapping\\s*\\(\\s*path\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'GetMapping': [\n", "            r'@GetMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@GetMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'PostMapping': [\n", "            r'@PostMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@PostMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'PutMapping': [\n", "            r'@PutMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'DeleteMapping': [\n", "            r'@DeleteMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ]\n", "    }\n", "    \n", "    for mapping_type, patterns in mapping_patterns.items():\n", "        for pattern in patterns:\n", "            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)\n", "            for match in matches:\n", "                if match.strip():\n", "                    endpoints.append({\n", "                        'type': mapping_type,\n", "                        'path': match.strip(),\n", "                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'\n", "                    })\n", "    \n", "    return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "    '''Extract database entities from JPA annotations'''\n", "    entities = []\n", "    \n", "    # Entity detection\n", "    entity_patterns = [\n", "        r'@Entity\\s*(?:\\([^)]*\\))?',\n", "        r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "    ]\n", "    \n", "    for pattern in entity_patterns:\n", "        if re.search(pattern, source_code_str, re.MULTILINE):\n", "            table_matches = re.findall(r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "            for table_name in table_matches:\n", "                if table_name.strip():\n", "                    entities.append({\n", "                        'type': 'table',\n", "                        'name': table_name.strip()\n", "                    })\n", "    \n", "    # Repository pattern detection\n", "    repository_pattern = r'interface\\s+(\\w+)\\s+extends\\s+.*Repository'\n", "    repo_matches = re.findall(repository_pattern, source_code_str)\n", "    for repo_name in repo_matches:\n", "        entity_name = repo_name.replace('Repository', '').replace('Rep', '')\n", "        if entity_name:\n", "            entities.append({\n", "                'type': 'table',\n", "                'name': entity_name.lower()\n", "            })\n", "    \n", "    return entities\n", "\n", "def extract_class_relationships(source_code_str):\n", "    '''Extract class inheritance relationships (v9 focused: prioritize EXTENDS)'''\n", "    relationships = []\n", "    \n", "    # Class extends (prioritized in v9)\n", "    class_extends_pattern = r'class\\s+(\\w+)\\s+extends\\s+([\\w<>]+)'\n", "    class_matches = re.findall(class_extends_pattern, source_code_str)\n", "    for child_class, parent_class in class_matches:\n", "        parent_class = re.sub(r'<.*?>', '', parent_class).strip()\n", "        if parent_class:\n", "            relationships.append({\n", "                'child_class': child_class,\n", "                'parent_class': parent_class,\n", "                'relationship_type': 'extends'\n", "            })\n", "    \n", "    # Class implements (minimal as per v9 preference)\n", "    implements_pattern = r'class\\s+(\\w+)(?:\\s+extends\\s+\\w+)?\\s+implements\\s+([\\w<>,\\s]+)'\n", "    impl_matches = re.findall(implements_pattern, source_code_str)\n", "    for class_name, implements_clause in impl_matches:\n", "        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]\n", "        for interface in interfaces:\n", "            if interface and len(interface) > 8:  # Only meaningful interfaces (v9 filter)\n", "                relationships.append({\n", "                    'child_class': class_name,\n", "                    'parent_class': interface,\n", "                    'relationship_type': 'implements'\n", "                })\n", "    \n", "    return relationships\n", "\n", "print('🔧 Stage 2: Utility functions loaded')"]}, {"cell_type": "code", "execution_count": 4, "id": "stage3_data_lineage", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Stage 3: Focused data lineage patterns loaded\n"]}], "source": ["# ========== STAGE 3: FOCUSED DATA LINEAGE PATTERNS (from v8 + v9 focus) ==========\n", "\n", "def extract_focused_data_lineage_patterns(source_code_str):\n", "    '''Extract data lineage patterns with v9 focused filtering'''\n", "    lineage_records = []\n", "    \n", "    # 1. SQL Query patterns - Data Sources (focused on meaningful operations)\n", "    sql_patterns = [\n", "        r'@Query\\s*\\(\\s*[\"\\']([^\"\\']*(SELECT|INSERT|UPDATE|DELETE)[^\"\\']*)[\"\\']',\n", "        r'@Query\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']*(SELECT|INSERT|UPDATE|DELETE)[^\"\\']*)[\"\\']',\n", "        r'@Query\\s*\\(\\s*nativeQuery\\s*=\\s*true\\s*,\\s*value\\s*=\\s*[\"\\']([^\"\\']*(SELECT|INSERT|UPDATE|DELETE)[^\"\\']*)[\"\\']'\n", "    ]\n", "    \n", "    for pattern in sql_patterns:\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE | re.IGNORECASE | re.DOTALL)\n", "        for match in matches:\n", "            if isinstance(match, tuple):\n", "                query = match[0]\n", "                operation = match[1].upper()\n", "            else:\n", "                query = match\n", "                operation = 'SELECT'\n", "            \n", "            # Extract table names from SQL\n", "            table_patterns = [\n", "                r'FROM\\s+(\\w+)',\n", "                r'JOIN\\s+(\\w+)',\n", "                r'UPDATE\\s+(\\w+)',\n", "                r'INSERT\\s+INTO\\s+(\\w+)',\n", "                r'DELETE\\s+FROM\\s+(\\w+)'\n", "            ]\n", "            \n", "            for table_pattern in table_patterns:\n", "                table_matches = re.findall(table_pattern, query, re.IGNORECASE)\n", "                for table_name in table_matches:\n", "                    lineage_records.append({\n", "                        'source_type': 'data',\n", "                        'source_name': table_name.lower(),\n", "                        'target_type': 'method',\n", "                        'target_name': f'{operation}_operation',\n", "                        'operation': operation,\n", "                        'lineage_type': 'data_find'\n", "                    })\n", "    \n", "    # 2. Focused Data Transformation patterns (v9 meaningful variables only)\n", "    transformation_patterns = [\n", "        # Service method calls that transform data\n", "        r'(\\w{5,})\\s*=\\s*(\\w+Service)\\s*\\.\\s*(\\w+)\\s*\\(',\n", "        # Repository operations\n", "        r'(\\w{5,})\\s*=\\s*(\\w+Repository)\\s*\\.\\s*(find|save|delete)\\w*\\s*\\(',\n", "        # Stream transformations (meaningful variables only)\n", "        r'(\\w{5,})\\s*=\\s*(\\w{5,})\\.stream\\s*\\(\\s*\\)\\.(?:map|filter|collect)\\s*\\(',\n", "        # Mapper transformations\n", "        r'(\\w{5,})\\s*=\\s*\\w*[Mm]apper\\s*\\.\\s*\\w+\\s*\\(\\s*(\\w{5,})'\n", "    ]\n", "    \n", "    for pattern in transformation_patterns:\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            if len(match) >= 2:\n", "                target_var = match[0]\n", "                source_identifier = match[1]\n", "                operation = match[2] if len(match) >= 3 else 'transform'\n", "                \n", "                if (is_meaningful_variable(target_var) and \n", "                    is_meaningful_variable(source_identifier) and \n", "                    target_var != source_identifier):\n", "                    \n", "                    lineage_records.append({\n", "                        'source_type': 'variable',\n", "                        'source_name': source_identifier,\n", "                        'target_type': 'variable',\n", "                        'target_name': target_var,\n", "                        'operation': operation,\n", "                        'lineage_type': 'transforms_to'\n", "                    })\n", "    \n", "    return lineage_records\n", "\n", "def build_focused_data_lineage_graph(source_code_str, class_name, app_name):\n", "    '''Build focused data lineage graph for a class'''\n", "    all_lineage = extract_focused_data_lineage_patterns(source_code_str)\n", "    \n", "    # Add class and application context\n", "    for record in all_lineage:\n", "        record['class_name'] = class_name\n", "        record['application'] = app_name\n", "    \n", "    return all_lineage\n", "\n", "print('📊 Stage 3: Focused data lineage patterns loaded')"]}, {"cell_type": "code", "execution_count": 5, "id": "stage4_class_registry", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Stage 4: Building enhanced class registry with focused filtering...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files: 100%|██████████| 19/19 [00:00<00:00, 39.11it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stage 4 Complete:\n", "   📁 19 classes processed\n", "   🌐 10 API endpoints found\n", "   🗄️ 3 database entities found\n", "   📊 8 focused data lineage patterns found\n", "   🔗 0 inter-application dependencies\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 4: ENHANCED CLASS REGISTRY WITH FOCUSED METADATA (from v8 + v9) ==========\n", "\n", "def build_enhanced_focused_class_registry():\n", "    '''Build comprehensive class registry with focused metadata filtering'''\n", "    class_registry = {}\n", "    inter_app_calls = []\n", "    \n", "    print('🔍 Stage 4: Building enhanced class registry with focused filtering...')\n", "    java_files = list(BASE_PATH.rglob('*.java'))\n", "    \n", "    for file_path in tqdm(java_files, desc='Processing files'):\n", "        app_name = detect_application(file_path)\n", "        \n", "        try:\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                source_code_str = f.read()\n", "            \n", "            package_name, imports = extract_package_and_imports(source_code_str)\n", "            endpoints = extract_api_endpoints(source_code_str)\n", "            db_entities = extract_database_entities(source_code_str)\n", "            class_relationships = extract_class_relationships(source_code_str)\n", "            \n", "            class_name = file_path.stem\n", "            fqcn = f'{package_name}.{class_name}' if package_name else class_name\n", "            \n", "            # Extract focused data lineage\n", "            data_lineage = build_focused_data_lineage_graph(source_code_str, class_name, app_name)\n", "            \n", "            # Detect inter-application dependencies\n", "            for imp in imports:\n", "                if app_name == 'ServiceBolt' and 'UnifiedBolt' in imp:\n", "                    inter_app_calls.append({\n", "                        'from_app': 'ServiceBolt',\n", "                        'to_app': 'UnifiedBolt',\n", "                        'from_class': class_name,\n", "                        'imported_class': imp.split('.')[-1],\n", "                        'import_type': 'dependency'\n", "                    })\n", "                elif app_name == 'UnifiedBolt' and 'ServiceBolt' in imp:\n", "                    inter_app_calls.append({\n", "                        'from_app': 'UnifiedBolt',\n", "                        'to_app': 'ServiceBolt',\n", "                        'from_class': class_name,\n", "                        'imported_class': imp.split('.')[-1],\n", "                        'import_type': 'dependency'\n", "                    })\n", "            \n", "            class_registry[class_name] = {\n", "                'fqcn': fqcn,\n", "                'package': package_name,\n", "                'file_path': str(file_path),\n", "                'application': app_name,\n", "                'imports': imports,\n", "                'endpoints': endpoints,\n", "                'db_entities': db_entities,\n", "                'class_relationships': class_relationships,\n", "                'data_lineage': data_lineage\n", "            }\n", "            \n", "        except Exception as e:\n", "            print(f'❌ Error processing {file_path.name}: {e}')\n", "            continue\n", "    \n", "    return class_registry, inter_app_calls\n", "\n", "class_registry, inter_app_calls = build_enhanced_focused_class_registry()\n", "\n", "# Summary statistics\n", "total_endpoints = sum(len(info.get('endpoints', [])) for info in class_registry.values())\n", "total_db_entities = sum(len(info.get('db_entities', [])) for info in class_registry.values())\n", "total_data_lineage = sum(len(info.get('data_lineage', [])) for info in class_registry.values())\n", "\n", "print(f'✅ Stage 4 Complete:')\n", "print(f'   📁 {len(class_registry)} classes processed')\n", "print(f'   🌐 {total_endpoints} API endpoints found')\n", "print(f'   🗄️ {total_db_entities} database entities found')\n", "print(f'   📊 {total_data_lineage} focused data lineage patterns found')\n", "print(f'   🔗 {len(inter_app_calls)} inter-application dependencies')"]}, {"cell_type": "code", "execution_count": 6, "id": "stage5_enhanced_ast", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌳 Stage 5: Extracting enhanced AST structures with focused filtering...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Enhanced AST Processing: 100%|██████████| 19/19 [00:00<00:00, 149.14it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stage 5 Complete: 423 enhanced relationships extracted\n", "   📊 Relationship types: {'declares': 190, 'transforms_to': 110, 'has_field': 107, 'exposes': 10, 'implements': 3, 'extends': 3}\n", "   🏢 Applications: {'UnifiedBolt': 334, 'ServiceBolt': 89}\n"]}], "source": ["# ========== STAGE 5: ENHANCED AST EXTRACTION WITH FOCUSED FILTERING (from v8 + v9) ==========\n", "\n", "def extract_enhanced_variable_transformations(source_code_str):\n", "    '''Extract variable transformations with v9 focused filtering'''\n", "    transformations = []\n", "    \n", "    assignment_patterns = [\n", "        r'(\\w{5,})\\s*=\\s*(\\w{5,})\\s*[+\\-*/]\\s*(\\w{5,})',  # binary operations (meaningful vars only)\n", "        r'(\\w{5,})\\s*=\\s*(\\w{5,})\\s*\\.\\s*(\\w+)\\s*\\(',  # method calls\n", "        r'(\\w{5,})\\s*=\\s*new\\s+(\\w+)\\s*\\(',  # object creation\n", "    ]\n", "    \n", "    for pattern in assignment_patterns:\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            if len(match) >= 3:\n", "                result_var = match[0]\n", "                input_vars = [match[1], match[2]]\n", "                \n", "                if (is_meaningful_variable(result_var) and \n", "                    all(is_meaningful_variable(var) for var in input_vars)):\n", "                    transformations.append({\n", "                        'result': result_var,\n", "                        'inputs': input_vars,\n", "                        'operation': 'transforms_to'\n", "                    })\n", "    \n", "    return transformations\n", "\n", "def extract_enhanced_focused_ast_structure(file_path):\n", "    '''Extract AST structure with v8 completeness and v9 focused filtering'''\n", "    records = []\n", "    source_code = read_source_code(file_path)\n", "    source_code_str = source_code.decode('utf-8')\n", "    tree = parser.parse(source_code)\n", "    root_node = tree.root_node\n", "    file_name = os.path.basename(file_path)\n", "    app_name = detect_application(file_path)\n", "    \n", "    transformations = extract_enhanced_variable_transformations(source_code_str)\n", "\n", "    def clean_node_name(name):\n", "        '''Clean node names'''\n", "        if not name:\n", "            return name\n", "        \n", "        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']\n", "        for prefix in prefixes_to_remove:\n", "            if name.lower().startswith(prefix):\n", "                name = name[len(prefix):]\n", "        \n", "        name = re.sub(r'\\.(java|class)$', '', name, flags=re.IGNORECASE)\n", "        return name.strip()\n", "\n", "    def traverse(node, parent_type=None, parent_name=None, context='global'):\n", "        # Handle class declarations\n", "        if node.type == 'class_declaration':\n", "            class_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    # File -> Class relationship\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'file',\n", "                        'destination_node': class_name,\n", "                        'destination_type': 'class',\n", "                        'relationship': 'declares',\n", "                        'file_path': str(file_path),\n", "                        'application': app_name\n", "                    })\n", "                    \n", "                    # Add metadata from registry with v9 focused approach\n", "                    class_info = class_registry.get(class_name, {})\n", "                    \n", "                    # API endpoints (EXPOSES relationship)\n", "                    for ep in class_info.get('endpoints', []):\n", "                        endpoint_name = f\"{ep['method']} {ep['path']}\"\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': endpoint_name,\n", "                            'destination_type': 'endpoint',\n", "                            'relationship': 'exposes',\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    \n", "                    # Database entities (DATA_FIND relationship)\n", "                    for entity in class_info.get('db_entities', []):\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': entity['name'],\n", "                            'destination_type': 'data',\n", "                            'relationship': 'data_find',\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    \n", "                    # Class relationships (v9 focused: prioritize EXTENDS)\n", "                    for rel in class_info.get('class_relationships', []):\n", "                        if rel['child_class'] == class_name:\n", "                            if rel['relationship_type'] == 'extends':\n", "                                records.append({\n", "                                    'source_node': class_name,\n", "                                    'source_type': 'class',\n", "                                    'destination_node': rel['parent_class'],\n", "                                    'destination_type': 'class',\n", "                                    'relationship': 'extends',\n", "                                    'file_path': str(file_path),\n", "                                    'application': app_name\n", "                                })\n", "                            # Minimal IMPLEMENTS (v9 preference)\n", "                            elif rel['relationship_type'] == 'implements' and len(rel['parent_class']) > 8:\n", "                                records.append({\n", "                                    'source_node': class_name,\n", "                                    'source_type': 'class',\n", "                                    'destination_node': rel['parent_class'],\n", "                                    'destination_type': 'interface',\n", "                                    'relationship': 'implements',\n", "                                    'file_path': str(file_path),\n", "                                    'application': app_name\n", "                                })\n", "                    \n", "                    # Data lineage (focused)\n", "                    for lineage in class_info.get('data_lineage', []):\n", "                        records.append({\n", "                            'source_node': lineage['source_name'],\n", "                            'source_type': lineage['source_type'],\n", "                            'destination_node': lineage['target_name'],\n", "                            'destination_type': lineage['target_type'],\n", "                            'relationship': lineage['lineage_type'],\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    break\n", "            \n", "            # Traverse children with class context\n", "            for child in node.children:\n", "                traverse(child, 'class', class_name, 'class')\n", "                \n", "        # Handle method declarations\n", "        elif node.type == 'method_declaration':\n", "            method_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    if parent_name and parent_type in ['class', 'interface']:\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': method_name,\n", "                            'destination_type': 'method',\n", "                            'relationship': 'declares',\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    break\n", "            \n", "            for child in node.children:\n", "                traverse(child, 'method', method_name, 'method')\n", "                \n", "        # Handle field declarations with v9 focused filtering\n", "        elif node.type == 'field_declaration':\n", "            for child in node.children:\n", "                if child.type == 'variable_declarator':\n", "                    for grandchild in child.children:\n", "                        if grandchild.type == 'identifier':\n", "                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))\n", "                            \n", "                            if is_meaningful_variable(field_name) and parent_name and parent_type == 'class':\n", "                                records.append({\n", "                                    'source_node': parent_name,\n", "                                    'source_type': parent_type,\n", "                                    'destination_node': field_name,\n", "                                    'destination_type': 'variable',\n", "                                    'relationship': 'has_field',\n", "                                    'file_path': str(file_path),\n", "                                    'application': app_name\n", "                                })\n", "        else:\n", "            # Continue traversing\n", "            for child in node.children:\n", "                traverse(child, parent_type, parent_name, context)\n", "\n", "    traverse(root_node)\n", "    \n", "    # Add focused transformation relationships\n", "    for transform in transformations:\n", "        for input_var in transform['inputs']:\n", "            records.append({\n", "                'source_node': input_var,\n", "                'source_type': 'variable',\n", "                'destination_node': transform['result'],\n", "                'destination_type': 'variable',\n", "                'relationship': 'transforms_to',\n", "                'file_path': str(file_path),\n", "                'application': app_name\n", "            })\n", "    \n", "    return records\n", "\n", "# Execute enhanced AST extraction\n", "print('🌳 Stage 5: Extracting enhanced AST structures with focused filtering...')\n", "ast_records = []\n", "java_files = list(BASE_PATH.rglob('*.java'))\n", "\n", "for file_path in tqdm(java_files, desc='Enhanced AST Processing'):\n", "    try:\n", "        ast_records.extend(extract_enhanced_focused_ast_structure(file_path))\n", "    except Exception as e:\n", "        print(f'❌ Error processing {file_path.name}: {e}')\n", "        continue\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "print(f'✅ Stage 5 Complete: {len(df_ast)} enhanced relationships extracted')\n", "if len(df_ast) > 0:\n", "    print(f'   📊 Relationship types: {df_ast[\"relationship\"].value_counts().to_dict()}')\n", "    print(f'   🏢 Applications: {df_ast[\"application\"].value_counts().to_dict()}')"]}, {"cell_type": "code", "execution_count": 7, "id": "stage6_llm_extraction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📄 Stage 6: Prepared 32 document chunks for LLM processing\n"]}], "source": ["# ========== STAGE 6: LLM EXTRACTION WITH FOCUSED CONTEXT (from v8 + v9) ==========\n", "\n", "def build_focused_system_prompt(file_path, ast_df, class_registry):\n", "    '''Build system prompt with focused context from v9'''\n", "    ast_subset = ast_df[ast_df['file_path'] == file_path] if len(ast_df) > 0 else pd.DataFrame()\n", "    ast_context = ''\n", "    for _, row in ast_subset.iterrows():\n", "        ast_context += f\"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']}\\n\"\n", "    \n", "    registry_context = 'Known Classes:\\n'\n", "    for class_name, info in class_registry.items():\n", "        registry_context += f'- {class_name} (FQCN: {info[\"fqcn\"]})\\n'\n", "        if len(info.get('endpoints', [])) > 0:\n", "            registry_context += f'  * {len(info[\"endpoints\"])} API endpoint(s)\\n'\n", "        if len(info.get('db_entities', [])) > 0:\n", "            registry_context += f'  * {len(info[\"db_entities\"])} DB entity/entities\\n'\n", "    \n", "    prompt = f\"\"\"\n", "You are a Java code lineage extraction engine. Extract relationships between code entities with FOCUSED approach:\n", "\n", "CONTEXT:\n", "{registry_context}\n", "\n", "AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):\n", "{ast_context}\n", "\n", "CRITICAL RULES - FOCUSED EXTRACTION (v9 approach):\n", "1. Use SIMPLE names only (remove prefixes like \"method:\", \"class:\", etc.)\n", "2. MAND<PERSON><PERSON>Y FOCUSED RELATIONSHIP DIRECTIONS:\n", "   - project -[contains]-> application\n", "   - application -[contains]-> folder\n", "   - folder -[contains]-> file\n", "   - file -[declares]-> class\n", "   - class -[declares]-> method  \n", "   - class -[has_field]-> variable (meaningful variables only)\n", "   - class -[exposes]-> endpoint\n", "   - class -[extends]-> class (prioritized)\n", "   - class -[implements]-> interface (minimal)\n", "   - class -[data_find]-> data\n", "   - variable -[transforms_to]-> variable (meaningful only)\n", "3. Extract REST API endpoints as 'endpoint' nodes (GET /api/users, POST /api/data)\n", "4. Extract database tables/entities as 'data' nodes\n", "5. Focus on MEANINGFUL variables only (length > 4 or business-relevant)\n", "6. Prioritize EXTENDS over IMPLEMENTS relationships\n", "7. Focus on global state changes over local variables\n", "8. Clean node names (remove \"method:\", \"class:\" prefixes)\n", "\n", "Extract triples in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the triples, no explanations.\n", "\"\"\"\n", "    return prompt\n", "\n", "def extract_focused_enhanced_variable_lineage(source_code_str, class_name, method_context=None):\n", "    '''Extract variable lineage with v8 method context and v9 focused filtering'''\n", "    variable_lineage = []\n", "    \n", "    # Enhanced variable assignment patterns with focused filtering\n", "    assignment_patterns = [\n", "        # Method return assignments: result = methodName(...) - meaningful vars only\n", "        r'(\\w{5,})\\s*=\\s*(\\w{5,})\\s*\\(.*?\\)',\n", "        # Service/Repository calls: result = serviceInstance.methodName(...)\n", "        r'(\\w{5,})\\s*=\\s*(\\w{5,})\\s*\\.\\s*(\\w+)\\s*\\(',\n", "        # Object creation: result = new ClassName(...)\n", "        r'(\\w{5,})\\s*=\\s*new\\s+(\\w+)\\s*\\(',\n", "        # Field access: result = object.fieldName\n", "        r'(\\w{5,})\\s*=\\s*(\\w{5,})\\s*\\.\\s*(\\w+)(?!\\s*\\()',\n", "        # Simple assignments: result = variable (meaningful only)\n", "        r'(\\w{5,})\\s*=\\s*(\\w{5,})(?!\\s*[\\(\\.])',\n", "        # Stream operations: result = source.stream().operation(...)\n", "        r'(\\w{5,})\\s*=\\s*(\\w{5,})\\.stream\\(\\)\\.(\\w+)\\(',\n", "    ]\n", "    \n", "    for pattern in assignment_patterns:\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            if len(match) >= 2:\n", "                result_var = match[0]\n", "                source_identifier = match[1]\n", "                operation = match[2] if len(match) >= 3 else 'assign'\n", "                \n", "                if (is_meaningful_variable(result_var) and \n", "                    is_meaningful_variable(source_identifier) and\n", "                    result_var != source_identifier):\n", "                    \n", "                    # Create enhanced variable name with context (v8 approach)\n", "                    result_with_context = f\"{class_name}.{method_context or 'field'}.{result_var}\" if method_context else f\"{class_name}.{result_var}\"\n", "                    source_with_context = f\"{class_name}.{method_context or 'field'}.{source_identifier}\" if method_context else f\"{class_name}.{source_identifier}\"\n", "                    \n", "                    variable_lineage.append({\n", "                        'source_node': source_with_context,\n", "                        'source_type': 'variable',\n", "                        'destination_node': result_with_context,\n", "                        'destination_type': 'variable',\n", "                        'relationship': 'transforms_to',\n", "                        'operation_context': operation,\n", "                        'class_context': class_name,\n", "                        'method_context': method_context\n", "                    })\n", "    \n", "    return variable_lineage\n", "\n", "def extract_focused_method_variable_context(source_code_str, class_name):\n", "    '''Extract method-level variable context with v9 focused filtering'''\n", "    method_variables = []\n", "    \n", "    # Find method declarations and their variables\n", "    method_pattern = r'(?:public|private|protected)?\\s*(?:static)?\\s*(?:\\w+\\s+)?(\\w{3,})\\s*\\([^)]*\\)\\s*\\{([^}]*(?:\\{[^}]*\\}[^}]*)*)\\}'\n", "    method_matches = re.findall(method_pattern, source_code_str, re.MULTILINE | re.DOTALL)\n", "    \n", "    for method_name, method_body in method_matches:\n", "        if is_meaningful_variable(method_name):  # Only meaningful method names\n", "            # Extract variables within this method\n", "            method_var_lineage = extract_focused_enhanced_variable_lineage(method_body, class_name, method_name)\n", "            method_variables.extend(method_var_lineage)\n", "    \n", "    return method_variables\n", "\n", "# Prepare for LLM extraction\n", "splitter = RecursiveCharacterTextSplitter.from_language(\n", "    language=LC_Language.JAVA,\n", "    chunk_size=4000,\n", "    chunk_overlap=200\n", ")\n", "\n", "java_docs, split_docs = [], []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            try:\n", "                loader = TextLoader(os.path.join(root, file))\n", "                java_docs.extend(loader.load())\n", "            except Exception as e:\n", "                print(f\" Error loading {file}: {e}\")\n", "                continue\n", "\n", "for doc in java_docs:\n", "    split_docs.extend(splitter.split_documents([doc]))\n", "\n", "print(f'📄 Stage 6: Prepared {len(split_docs)} document chunks for LLM processing')"]}, {"cell_type": "code", "execution_count": 8, "id": "stage7_llm_processing", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤖 Stage 7: Starting focused LLM extraction with enhanced variable context...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Focused LLM Extraction: 100%|██████████| 32/32 [05:15<00:00,  9.86s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stage 7 Complete: 892 focused LLM relationships extracted\n", "   📊 Enhanced variable lineage: 323\n", "   🔗 Traditional relationships: 569\n", "   🏢 Applications: {'UnifiedBolt': 559, 'ServiceBolt': 241}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 7: FOCUSED LLM PROCESSING (from v8 + v9) ==========\n", "\n", "# Initialize focused LLM lineage collection\n", "all_llm_lineage = []\n", "\n", "print('🤖 Stage 7: Starting focused LLM extraction with enhanced variable context...')\n", "\n", "for chunk in tqdm(split_docs, desc='Focused LLM Extraction'):\n", "    file_path = chunk.metadata.get('source')\n", "    system_prompt = build_focused_system_prompt(file_path, df_ast, class_registry)\n", "    \n", "    # Extract class name from file path for context\n", "    file_name = os.path.basename(file_path) if file_path else 'unknown'\n", "    class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name\n", "    \n", "    # Extract focused enhanced variable lineage with method context\n", "    enhanced_var_lineage = extract_focused_method_variable_context(chunk.page_content, class_name)\n", "    all_llm_lineage.extend(enhanced_var_lineage)\n", "    \n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=system_prompt,\n", "        allowed_nodes=['project', 'application', 'folder', 'file', 'class', 'method', 'interface', 'variable', 'endpoint', 'data'],\n", "        allowed_relationships=[\n", "            ('project', 'contains', 'application'),\n", "            ('application', 'contains', 'folder'),\n", "            ('folder', 'contains', 'folder'),\n", "            ('folder', 'contains', 'file'),\n", "            ('file', 'declares', 'class'),\n", "            ('file', 'declares', 'interface'),\n", "            ('class', 'declares', 'method'),\n", "            ('class', 'declares_variable', 'variable'),\n", "            ('class', 'exposes', 'endpoint'),\n", "            ('class', 'extends', 'class'),\n", "            ('class', 'implements', 'interface'),\n", "            ('class', 'has_field', 'variable'),\n", "            ('method', 'uses', 'variable'),\n", "            ('variable', 'transforms_to', 'variable'),\n", "            ('class', 'data_find', 'data'),\n", "            ('method', 'data_find', 'data'),\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False,\n", "    )\n", "    \n", "    try:\n", "        graph_docs = transformer.convert_to_graph_documents([chunk])\n", "        for gd in graph_docs:\n", "            for rel in gd.relationships:\n", "                s_node = rel.source.id.strip()\n", "                s_type = rel.source.type.strip().lower()\n", "                t_node = rel.target.id.strip()\n", "                t_type = rel.target.type.strip().lower()\n", "                rel_type = rel.type.strip().lower()\n", "\n", "                def normalize_entity(entity_name, entity_type):\n", "                    if not entity_name:\n", "                        return entity_name\n", "                    \n", "                    # Remove prefixes\n", "                    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "                    for prefix in prefixes:\n", "                        if entity_name.lower().startswith(prefix):\n", "                            entity_name = entity_name[len(prefix):]\n", "                    \n", "                    # Remove file extensions\n", "                    entity_name = re.sub(r'\\.(java|class)$', '', entity_name, flags=re.IGNORECASE)\n", "                    \n", "                    # Clean dots for class names\n", "                    if entity_type in ['class', 'method'] and '.' in entity_name:\n", "                        entity_name = entity_name.split('.')[-1]\n", "                    \n", "                    # Match with class registry for consistency\n", "                    if entity_type == 'class':\n", "                        for class_name in class_registry.keys():\n", "                            if entity_name.lower() == class_name.lower():\n", "                                return class_name.lower()\n", "                    \n", "                    return entity_name.lower()\n", "\n", "                s_node = normalize_entity(s_node, s_type)\n", "                t_node = normalize_entity(t_node, t_type)\n", "\n", "                # Skip invalid relationships\n", "                if s_node == t_node and s_type == t_type:\n", "                    continue\n", "                if not s_node or not t_node:\n", "                    continue\n", "                \n", "                # Enforce focused relationship directions (v9 approach)\n", "                valid_directions = {\n", "                    ('project', 'contains', 'application'),\n", "                    ('application', 'contains', 'folder'),\n", "                    ('folder', 'contains', 'folder'),\n", "                    ('folder', 'contains', 'file'),\n", "                    ('file', 'declares', 'class'),\n", "                    ('file', 'declares', 'interface'),\n", "                    ('class', 'declares', 'method'),\n", "                    ('class', 'declares_variable', 'variable'),\n", "                    ('class', 'exposes', 'endpoint'),\n", "                    ('class', 'has_field', 'variable'),\n", "                    ('method', 'uses', 'variable'),\n", "                    ('class', 'extends', 'class'),\n", "                    ('class', 'implements', 'interface'),\n", "                    ('variable', 'transforms_to', 'variable'),\n", "                    ('class', 'data_find', 'data'),\n", "                    ('method', 'data_find', 'data')\n", "                }\n", "                \n", "                if (s_type, rel_type, t_type) not in valid_directions:\n", "                    continue\n", "\n", "                all_llm_lineage.append({\n", "                    'source_node': s_node,\n", "                    'source_type': s_type,\n", "                    'destination_node': t_node,\n", "                    'destination_type': t_type,\n", "                    'relationship': rel_type,\n", "                    'file_path': file_path,\n", "                    'application': detect_application(file_path) if file_path else 'Unknown'\n", "                })\n", "    except Exception as e:\n", "        continue\n", "\n", "df_llm = pd.DataFrame(all_llm_lineage)\n", "print(f'✅ Stage 7 Complete: {len(df_llm)} focused LLM relationships extracted')\n", "if len(df_llm) > 0:\n", "    print(f'   📊 Enhanced variable lineage: {len([r for r in all_llm_lineage if \"transforms_to\" in r.get(\"relationship\", \"\")])}')\n", "    print(f'   🔗 Traditional relationships: {len([r for r in all_llm_lineage if \"transforms_to\" not in r.get(\"relationship\", \"\")])}')\n", "    print(f'   🏢 Applications: {df_llm[\"application\"].value_counts().to_dict()}')"]}, {"cell_type": "code", "execution_count": 9, "id": "stage8_final_combination", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔄 Stage 8: Combining all focused enhanced data sources...\n", "📁 Added 8 folder relationships\n", "📄 Added 19 file relationships\n", "🏢 Added 2 application relationships\n", "🤖 Added 892 focused LLM relationships\n", "🔗 Added 0 inter-application dependencies\n", "\n", "✅ Final focused enhanced dataset created:\n", "   📊 Total relationships: 921\n", "   🏢 Applications: {'UnifiedBolt': 581, 'ServiceBolt': 248}\n", "   🔗 Relationship types: {'transforms_to': 323, 'declares': 294, 'has_field': 237, 'contains': 29, 'exposes': 24, 'implements': 9, 'extends': 5}\n", "\n", "🚀 Loading to Neo4j...\n", "🧹 Clearing existing data...\n", "📊 Loading focused enhanced data...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading relationships: 100%|██████████| 7/7 [00:04<00:00,  1.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Focused enhanced data successfully loaded to Neo4j\n", "\n", "📊 Neo4j Node Statistics:\n", "   Variable: 254 nodes\n", "   Method: 146 nodes\n", "   File: 25 nodes\n", "   Class: 14 nodes\n", "   Endpoint: 10 nodes\n", "   Folder: 8 nodes\n", "   Interface: 3 nodes\n", "   Application: 2 nodes\n", "   Project: 2 nodes\n", "\n", "🔗 Neo4j Relationship Statistics:\n", "   DECLARES: 174 relationships\n", "   TRANSFORMS_TO: 146 relationships\n", "   HAS_FIELD: 105 relationships\n", "   CONTAINS: 29 relationships\n", "   EXPOSES: 10 relationships\n", "   IMPLEMENTS: 3 relationships\n", "   EXTENDS: 3 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 8: FINAL DATA COMBINATION AND NEO4J LOADING (from v8 + v9) ==========\n", "\n", "def combine_all_focused_enhanced_data():\n", "    '''Combine all data sources with v8 completeness and v9 focused filtering'''\n", "    all_records = []\n", "    \n", "    # Add hierarchy records (PROJECT -> APPLICATION -> FOLDER -> FILE)\n", "    if 'df_folders' in globals() and len(df_folders) > 0:\n", "        all_records.extend(df_folders.to_dict('records'))\n", "        print(f'📁 Added {len(df_folders)} folder relationships')\n", "    \n", "    if 'df_files' in globals() and len(df_files) > 0:\n", "        all_records.extend(df_files.to_dict('records'))\n", "        print(f'📄 Added {len(df_files)} file relationships')\n", "    \n", "    if 'df_apps' in globals() and len(df_apps) > 0:\n", "        all_records.extend(df_apps.to_dict('records'))\n", "        print(f'🏢 Added {len(df_apps)} application relationships')\n", "    \n", "    # Add LLM records (focused extraction)\n", "    if 'df_llm' in globals() and len(df_llm) > 0:\n", "        all_records.extend(df_llm.to_dict('records'))\n", "        print(f'🤖 Added {len(df_llm)} focused LLM relationships')\n", "    \n", "    # Add inter-application dependencies\n", "    for call in inter_app_calls:\n", "        all_records.append({\n", "            'source_node': call['from_class'],\n", "            'source_type': 'class',\n", "            'destination_node': call['imported_class'],\n", "            'destination_type': 'class',\n", "            'relationship': 'uses',\n", "            'file_path': None,\n", "            'application': call['from_app']\n", "        })\n", "    \n", "    print(f'🔗 Added {len(inter_app_calls)} inter-application dependencies')\n", "    \n", "    return pd.DataFrame(all_records)\n", "\n", "def load_to_focused_neo4j(df_final):\n", "    '''Load data to Neo4j with v9 focused node types and relationships'''\n", "    if graph is None:\n", "        print('❌ Neo4j connection not available')\n", "        return\n", "    \n", "    try:\n", "        # Clear existing data\n", "        print('🧹 Clearing existing data...')\n", "        graph.query('MATCH (n) DETACH DELETE n')\n", "        \n", "        # Create nodes and relationships with v9 focused labels\n", "        print('📊 Loading focused enhanced data...')\n", "        \n", "        # Map our node types to Neo4j labels (v9 focused)\n", "        node_type_mapping = {\n", "            'project': 'Project',\n", "            'application': 'Application', \n", "            'folder': 'Folder',\n", "            'file': 'File',\n", "            'class': 'Class',\n", "            'method': 'Method',\n", "            'interface': 'Interface',\n", "            'variable': 'Variable',\n", "            'endpoint': 'Endpoint',\n", "            'data': 'Data'\n", "        }\n", "        \n", "        # Map our relationships to Neo4j relationship types (v9 focused)\n", "        relationship_mapping = {\n", "            'contains': 'CONTAINS',\n", "            'declares': 'DECLARES',\n", "            'declares_variable': 'DECLARES_VARIABLE',\n", "            'exposes': 'EXPOSES',\n", "            'extends': 'EXTENDS',\n", "            'implements': 'IMPLEMENTS',\n", "            'has_field': 'HAS_FIELD',\n", "            'uses': 'USES',\n", "            'transforms_to': 'TRANSFORMS_TO',\n", "            'data_find': 'DATA_FIND'\n", "        }\n", "        \n", "        # Group by relationship type for efficient loading\n", "        relationship_groups = df_final.groupby('relationship')\n", "        \n", "        for rel_type, group in tqdm(relationship_groups, desc='Loading relationships'):\n", "            for _, row in group.iterrows():\n", "                try:\n", "                    source_label = node_type_mapping.get(row['source_type'], row['source_type'].title())\n", "                    target_label = node_type_mapping.get(row['destination_type'], row['destination_type'].title())\n", "                    neo4j_rel_type = relationship_mapping.get(rel_type, rel_type.upper().replace(' ', '_'))\n", "                    \n", "                    # Create nodes and relationship\n", "                    cypher = f\"\"\"\n", "                    MERGE (source:{source_label} {{name: $source_name, application: $app}})\n", "                    MERGE (target:{target_label} {{name: $target_name, application: $app}})\n", "                    MERGE (source)-[:{neo4j_rel_type}]->(target)\n", "                    \"\"\"\n", "                    \n", "                    graph.query(cypher, {\n", "                        'source_name': str(row['source_node']),\n", "                        'target_name': str(row['destination_node']),\n", "                        'app': str(row['application'])\n", "                    })\n", "                \n", "                except Exception as e:\n", "                    print(f'⚠️ Error loading relationship: {e}')\n", "                    continue\n", "        \n", "        # Create indexes for performance\n", "        indexes = [\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:File) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Data) ON (n.name)'\n", "        ]\n", "        \n", "        for index in indexes:\n", "            try:\n", "                graph.query(index)\n", "            except:\n", "                pass\n", "        \n", "        print('✅ Focused enhanced data successfully loaded to Neo4j')\n", "        \n", "        # Get statistics\n", "        stats = graph.query(\"\"\"\n", "        MATCH (n)\n", "        RETURN labels(n)[0] as node_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('\\n📊 Neo4j Node Statistics:')\n", "        for stat in stats:\n", "            print(f'   {stat[\"node_type\"]}: {stat[\"count\"]} nodes')\n", "        \n", "        # Get relationship statistics\n", "        rel_stats = graph.query(\"\"\"\n", "        MATCH ()-[r]->()\n", "        RETURN type(r) as relationship_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('\\n🔗 Neo4j Relationship Statistics:')\n", "        for stat in rel_stats:\n", "            print(f'   {stat[\"relationship_type\"]}: {stat[\"count\"]} relationships')\n", "    \n", "    except Exception as e:\n", "        print(f'❌ Error loading to Neo4j: {e}')\n", "\n", "# Execute final data combination and loading\n", "print('\\n🔄 Stage 8: Combining all focused enhanced data sources...')\n", "df_final = combine_all_focused_enhanced_data()\n", "\n", "print(f'\\n✅ Final focused enhanced dataset created:')\n", "print(f'   📊 Total relationships: {len(df_final)}')\n", "if len(df_final) > 0:\n", "    print(f'   🏢 Applications: {df_final[\"application\"].value_counts().to_dict()}')\n", "    print(f'   🔗 Relationship types: {df_final[\"relationship\"].value_counts().to_dict()}')\n", "\n", "# Load to Neo4j\n", "print('\\n🚀 Loading to Neo4j...')\n", "load_to_focused_neo4j(df_final)"]}, {"cell_type": "code", "execution_count": null, "id": "stage9_csv_export_and_fix", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 9: CSV EXPORT AND CONNECTION FIXES ==========\n", "\n", "def export_and_fix_connections():\n", "    '''Export current data to CSV and fix connection issues'''\n", "    \n", "    # First, let's export the current final dataset to CSV for analysis\n", "    print('📊 Exporting current dataset to CSV for analysis...')\n", "    df_final.to_csv('current_relationships.csv', index=False)\n", "    print(f'✅ Exported {len(df_final)} relationships to current_relationships.csv')\n", "    \n", "    # Analyze current issues\n", "    print('\\n🔍 Analyzing connection issues...')\n", "    \n", "    # Issue 1: Check file-to-folder connections\n", "    file_relationships = df_final[df_final['destination_type'] == 'file']\n", "    print(f'📁 Current file relationships: {len(file_relationships)}')\n", "    \n", "    # Issue 2: Check variable connections to classes/methods\n", "    variable_relationships = df_final[df_final['source_type'] == 'variable']\n", "    isolated_variables = variable_relationships[~variable_relationships['source_node'].str.contains('\\\\.')]\n", "    print(f'🔗 Variable relationships: {len(variable_relationships)}')\n", "    print(f'⚠️ Isolated variables (not connected to class/method): {len(isolated_variables)}')\n", "    \n", "    return df_final\n", "\n", "def fix_file_folder_connections(df):\n", "    '''Fix missing file-to-folder connections'''\n", "    print('\\n🔧 Fixing file-to-folder connections...')\n", "    \n", "    fixed_records = []\n", "    \n", "    # Get all files and their paths\n", "    file_records = df[df['destination_type'] == 'file'].copy()\n", "    \n", "    for _, file_record in file_records.iterrows():\n", "        file_name = file_record['destination_node']\n", "        file_path = file_record.get('file_path', '')\n", "        app_name = file_record['application']\n", "        \n", "        if file_path and '/' in file_path:\n", "            # Extract the immediate parent folder\n", "            path_parts = file_path.replace('\\\\\\\\', '/').split('/')\n", "            if len(path_parts) > 1:\n", "                parent_folder = path_parts[-2]  # Immediate parent folder\n", "                \n", "                # Create proper folder -> file connection\n", "                fixed_records.append({\n", "                    'source_node': parent_folder,\n", "                    'source_type': 'folder',\n", "                    'destination_node': file_name,\n", "                    'destination_type': 'file',\n", "                    'relationship': 'contains',\n", "                    'file_path': file_path,\n", "                    'application': app_name\n", "                })\n", "    \n", "    print(f'✅ Fixed {len(fixed_records)} file-to-folder connections')\n", "    return fixed_records\n", "\n", "def fix_variable_class_connections(df):\n", "    '''Fix variable connections to their containing classes/methods'''\n", "    print('\\n🔧 Fixing variable-to-class/method connections...')\n", "    \n", "    fixed_records = []\n", "    \n", "    # Get all variable transformations\n", "    var_transforms = df[df['relationship'] == 'transforms_to'].copy()\n", "    \n", "    for _, var_record in var_transforms.iterrows():\n", "        source_var = var_record['source_node']\n", "        target_var = var_record['destination_node']\n", "        file_path = var_record.get('file_path', '')\n", "        app_name = var_record['application']\n", "        \n", "        # Extract class name from file path\n", "        if file_path:\n", "            file_name = os.path.basename(file_path)\n", "            class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name\n", "            \n", "            # If variables don't already have class context, add it\n", "            if '.' not in source_var and '.' not in target_var:\n", "                # Create class -> variable connections\n", "                fixed_records.append({\n", "                    'source_node': class_name,\n", "                    'source_type': 'class',\n", "                    'destination_node': source_var,\n", "                    'destination_type': 'variable',\n", "                    'relationship': 'declares_variable',\n", "                    'file_path': file_path,\n", "                    'application': app_name\n", "                })\n", "                \n", "                fixed_records.append({\n", "                    'source_node': class_name,\n", "                    'source_type': 'class',\n", "                    'destination_node': target_var,\n", "                    'destination_type': 'variable',\n", "                    'relationship': 'declares_variable',\n", "                    'file_path': file_path,\n", "                    'application': app_name\n", "                })\n", "    \n", "    print(f'✅ Fixed {len(fixed_records)} variable-to-class connections')\n", "    return fixed_records\n", "\n", "def create_method_variable_connections(df):\n", "    '''Create proper method -> variable connections'''\n", "    print('\\n🔧 Creating method-to-variable connections...')\n", "    \n", "    fixed_records = []\n", "    \n", "    # Get all methods\n", "    methods = df[df['destination_type'] == 'method'].copy()\n", "    \n", "    # Get all variables with context\n", "    variables = df[df['source_type'] == 'variable'].copy()\n", "    \n", "    for _, method_record in methods.iterrows():\n", "        method_name = method_record['destination_node']\n", "        class_name = method_record['source_node']\n", "        file_path = method_record.get('file_path', '')\n", "        app_name = method_record['application']\n", "        \n", "        # Find variables that should belong to this method\n", "        method_variables = variables[\n", "            (variables['source_node'].str.contains(f'{class_name}.{method_name}.', na=False)) |\n", "            (variables['destination_node'].str.contains(f'{class_name}.{method_name}.', na=False))\n", "        ]\n", "        \n", "        for _, var_record in method_variables.iterrows():\n", "            # Extract the actual variable name\n", "            var_name = var_record['source_node'].split('.')[-1] if '.' in var_record['source_node'] else var_record['source_node']\n", "            \n", "            # Create method -> variable connection\n", "            fixed_records.append({\n", "                'source_node': method_name,\n", "                'source_type': 'method',\n", "                'destination_node': var_name,\n", "                'destination_type': 'variable',\n", "                'relationship': 'uses',\n", "                'file_path': file_path,\n", "                'application': app_name\n", "            })\n", "    \n", "    print(f'✅ Created {len(fixed_records)} method-to-variable connections')\n", "    return fixed_records\n", "\n", "# Execute the fixes\n", "print('🔧 Starting connection fixes...')\n", "\n", "# Export current state\n", "current_df = export_and_fix_connections()\n", "\n", "# Apply fixes\n", "file_fixes = fix_file_folder_connections(current_df)\n", "variable_fixes = fix_variable_class_connections(current_df)\n", "method_fixes = create_method_variable_connections(current_df)\n", "\n", "# Combine all fixes\n", "all_fixes = file_fixes + variable_fixes + method_fixes\n", "df_fixes = pd.DataFrame(all_fixes)\n", "\n", "print(f'\\n📊 Total fixes generated: {len(df_fixes)}')\n", "if len(df_fixes) > 0:\n", "    print(f'   🔗 Fix types: {df_fixes[\"relationship\"].value_counts().to_dict()}')\n", "\n", "# Export fixes to CSV\n", "if len(df_fixes) > 0:\n", "    df_fixes.to_csv('connection_fixes.csv', index=False)\n", "    print('✅ Exported fixes to connection_fixes.csv')"]}, {"cell_type": "code", "execution_count": 10, "id": "stage10_apply_fixes", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔧 Applying all fixes...\n", "🔄 Combining original data with fixes...\n", "📊 Removed 453 duplicates\n", "\n", "✅ Final fixed dataset:\n", "   📊 Total relationships: 468\n", "   🔗 Relationship types: {'declares': 174, 'transforms_to': 144, 'has_field': 105, 'contains': 29, 'exposes': 10, 'implements': 3, 'extends': 3}\n", "   🏢 Applications: {'UnifiedBolt': 332, 'ServiceBolt': 73}\n", "✅ Exported final fixed dataset to final_fixed_relationships.csv\n", "\n", "🚀 Loading fixed data to Neo4j...\n", "🧹 Clearing existing data...\n", "📊 Loading fixed data to Neo4j...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading batches: 100%|██████████| 5/5 [00:07<00:00,  1.43s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Fixed data successfully loaded to Neo4j\n", "\n", "📊 Final Neo4j Node Statistics:\n", "   Variable: 252 nodes\n", "   Method: 146 nodes\n", "   File: 25 nodes\n", "   Class: 14 nodes\n", "   Endpoint: 10 nodes\n", "   Folder: 8 nodes\n", "   Interface: 3 nodes\n", "   Application: 2 nodes\n", "   Project: 2 nodes\n", "\n", "🔗 Final Neo4j Relationship Statistics:\n", "   DECLARES: 174 relationships\n", "   TRANSFORMS_TO: 144 relationships\n", "   HAS_FIELD: 105 relationships\n", "   CONTAINS: 29 relationships\n", "   EXPOSES: 10 relationships\n", "   IMPLEMENTS: 3 relationships\n", "   EXTENDS: 3 relationships\n", "\n", "🎉 CONNECTION FIXES COMPLETE!\n", "\n", "✅ Fixed Issues:\n", "   🔗 File-to-folder connections properly established\n", "   🔗 Variables now connected to their containing classes\n", "   🔗 Method-to-variable relationships created\n", "   🔗 Proper hierarchical structure maintained\n", "\n", "📁 CSV Files Generated:\n", "   📄 current_relationships.csv - Original data\n", "   📄 connection_fixes.csv - Applied fixes\n", "   📄 final_fixed_relationships.csv - Complete fixed dataset\n"]}], "source": ["# ========== STAGE 10: APPLY FIXES AND RELOAD TO NEO4J ==========\n", "\n", "def combine_with_fixes():\n", "    '''Combine original data with fixes'''\n", "    print('🔄 Combining original data with fixes...')\n", "    \n", "    # Start with original data\n", "    all_records = df_final.to_dict('records')\n", "    \n", "    # Add fixes if they exist\n", "    if 'df_fixes' in globals() and len(df_fixes) > 0:\n", "        all_records.extend(df_fixes.to_dict('records'))\n", "        print(f'✅ Added {len(df_fixes)} fix records')\n", "    \n", "    # Remove duplicates based on key fields\n", "    seen = set()\n", "    unique_records = []\n", "    \n", "    for record in all_records:\n", "        key = (record['source_node'], record['source_type'], \n", "               record['destination_node'], record['destination_type'], \n", "               record['relationship'])\n", "        if key not in seen:\n", "            seen.add(key)\n", "            unique_records.append(record)\n", "    \n", "    print(f'📊 Removed {len(all_records) - len(unique_records)} duplicates')\n", "    \n", "    return pd.DataFrame(unique_records)\n", "\n", "def load_fixed_data_to_neo4j(df_fixed):\n", "    '''Load the fixed data to Neo4j'''\n", "    if graph is None:\n", "        print('❌ Neo4j connection not available')\n", "        return\n", "    \n", "    try:\n", "        # Clear existing data\n", "        print('🧹 Clearing existing data...')\n", "        graph.query('MATCH (n) DETACH DELETE n')\n", "        \n", "        # Load fixed data\n", "        print('📊 Loading fixed data to Neo4j...')\n", "        \n", "        # Node type mapping\n", "        node_type_mapping = {\n", "            'project': 'Project',\n", "            'application': 'Application', \n", "            'folder': 'Folder',\n", "            'file': 'File',\n", "            'class': 'Class',\n", "            'method': 'Method',\n", "            'interface': 'Interface',\n", "            'variable': 'Variable',\n", "            'endpoint': 'Endpoint',\n", "            'data': 'Data'\n", "        }\n", "        \n", "        # Relationship mapping\n", "        relationship_mapping = {\n", "            'contains': 'CONTAINS',\n", "            'declares': 'DECLARES',\n", "            'declares_variable': 'DECLARES_VARIABLE',\n", "            'exposes': 'EXPOSES',\n", "            'extends': 'EXTENDS',\n", "            'implements': 'IMPLEMENTS',\n", "            'has_field': 'HAS_FIELD',\n", "            'uses': 'USES',\n", "            'transforms_to': 'TRANSFORMS_TO',\n", "            'data_find': 'DATA_FIND'\n", "        }\n", "        \n", "        # Load in batches for better performance\n", "        batch_size = 100\n", "        total_batches = len(df_fixed) // batch_size + (1 if len(df_fixed) % batch_size > 0 else 0)\n", "        \n", "        for i in tqdm(range(0, len(df_fixed), batch_size), desc='Loading batches', total=total_batches):\n", "            batch = df_fixed.iloc[i:i+batch_size]\n", "            \n", "            for _, row in batch.iterrows():\n", "                try:\n", "                    source_label = node_type_mapping.get(row['source_type'], row['source_type'].title())\n", "                    target_label = node_type_mapping.get(row['destination_type'], row['destination_type'].title())\n", "                    neo4j_rel_type = relationship_mapping.get(row['relationship'], row['relationship'].upper().replace(' ', '_'))\n", "                    \n", "                    # Create nodes and relationship\n", "                    cypher = f\"\"\"\n", "                    MERGE (source:{source_label} {{name: $source_name, application: $app}})\n", "                    MERGE (target:{target_label} {{name: $target_name, application: $app}})\n", "                    MERGE (source)-[:{neo4j_rel_type}]->(target)\n", "                    \"\"\"\n", "                    \n", "                    graph.query(cypher, {\n", "                        'source_name': str(row['source_node']),\n", "                        'target_name': str(row['destination_node']),\n", "                        'app': str(row['application'])\n", "                    })\n", "                \n", "                except Exception as e:\n", "                    continue\n", "        \n", "        # Create indexes\n", "        indexes = [\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:File) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Variable) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Data) ON (n.name)'\n", "        ]\n", "        \n", "        for index in indexes:\n", "            try:\n", "                graph.query(index)\n", "            except:\n", "                pass\n", "        \n", "        print('✅ Fixed data successfully loaded to Neo4j')\n", "        \n", "        # Get final statistics\n", "        stats = graph.query(\"\"\"\n", "        MATCH (n)\n", "        RETURN labels(n)[0] as node_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('\\n📊 Final Neo4j Node Statistics:')\n", "        for stat in stats:\n", "            print(f'   {stat[\"node_type\"]}: {stat[\"count\"]} nodes')\n", "        \n", "        rel_stats = graph.query(\"\"\"\n", "        MATCH ()-[r]->()\n", "        RETURN type(r) as relationship_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('\\n🔗 Final Neo4j Relationship Statistics:')\n", "        for stat in rel_stats:\n", "            print(f'   {stat[\"relationship_type\"]}: {stat[\"count\"]} relationships')\n", "    \n", "    except Exception as e:\n", "        print(f'❌ Error loading fixed data to Neo4j: {e}')\n", "\n", "# Apply the fixes\n", "print('\\n🔧 Applying all fixes...')\n", "df_final_fixed = combine_with_fixes()\n", "\n", "print(f'\\n✅ Final fixed dataset:')\n", "print(f'   📊 Total relationships: {len(df_final_fixed)}')\n", "if len(df_final_fixed) > 0:\n", "    print(f'   🔗 Relationship types: {df_final_fixed[\"relationship\"].value_counts().to_dict()}')\n", "    print(f'   🏢 Applications: {df_final_fixed[\"application\"].value_counts().to_dict()}')\n", "\n", "# Export final fixed dataset\n", "df_final_fixed.to_csv('final_fixed_relationships.csv', index=False)\n", "print(f'✅ Exported final fixed dataset to final_fixed_relationships.csv')\n", "\n", "# Load to Neo4j\n", "print('\\n🚀 Loading fixed data to Neo4j...')\n", "load_fixed_data_to_neo4j(df_final_fixed)\n", "\n", "print('\\n🎉 CONNECTION FIXES COMPLETE!')\n", "print('\\n✅ Fixed Issues:')\n", "print('   🔗 File-to-folder connections properly established')\n", "print('   🔗 Variables now connected to their containing classes')\n", "print('   🔗 Method-to-variable relationships created')\n", "print('   🔗 Proper hierarchical structure maintained')\n", "print('\\n📁 CSV Files Generated:')\n", "print('   📄 current_relationships.csv - Original data')\n", "print('   📄 connection_fixes.csv - Applied fixes')\n", "print('   📄 final_fixed_relationships.csv - Complete fixed dataset')"]}, {"cell_type": "code", "execution_count": 13, "id": "266e2cd3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 468 entries, 0 to 467\n", "Data columns (total 10 columns):\n", " #   Column             Non-Null Count  Dtype \n", "---  ------             --------------  ----- \n", " 0   source_node        468 non-null    object\n", " 1   source_type        468 non-null    object\n", " 2   destination_node   468 non-null    object\n", " 3   destination_type   468 non-null    object\n", " 4   relationship       468 non-null    object\n", " 5   file_path          395 non-null    object\n", " 6   application        405 non-null    object\n", " 7   operation_context  63 non-null     object\n", " 8   class_context      63 non-null     object\n", " 9   method_context     63 non-null     object\n", "dtypes: object(10)\n", "memory usage: 36.7+ KB\n"]}], "source": ["df_final_fixed.info()"]}, {"cell_type": "code", "execution_count": 15, "id": "stage11_standardization_fixes", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 Starting standardization fixes...\n", "\n", "🔧 Applying standardization fixes...\n", "🔧 Fixing project-to-application connections...\n", "✅ Created 2 project-to-application connections\n", "🔧 Standardizing variable names...\n"]}, {"ename": "TypeError", "evalue": "expected str, bytes or os.PathLike object, not float", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 197\u001b[39m\n\u001b[32m    194\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m'\u001b[39m\u001b[33m🔧 Starting standardization fixes...\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m    196\u001b[39m \u001b[38;5;66;03m# Apply fixes to the current final dataset\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m197\u001b[39m df_standardized_final = \u001b[43mapply_standardization_fixes\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdf_final_fixed\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    199\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m✅ Standardization complete:\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m    200\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m'\u001b[39m\u001b[33m   📊 Total relationships: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(df_standardized_final)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 165\u001b[39m, in \u001b[36mapply_standardization_fixes\u001b[39m\u001b[34m(df)\u001b[39m\n\u001b[32m    162\u001b[39m project_fixes = fix_project_application_connections()\n\u001b[32m    164\u001b[39m \u001b[38;5;66;03m# Fix 2: Standardize variable names\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m165\u001b[39m df_standardized, variable_mapping = \u001b[43mstandardize_variable_names\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    167\u001b[39m \u001b[38;5;66;03m# Fix 3: Create proper variable connections\u001b[39;00m\n\u001b[32m    168\u001b[39m variable_connections = create_proper_variable_connections(df_standardized, variable_mapping)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 50\u001b[39m, in \u001b[36mstandardize_variable_names\u001b[39m\u001b[34m(df)\u001b[39m\n\u001b[32m     48\u001b[39m class_name = \u001b[33m'\u001b[39m\u001b[33mUnknown\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m     49\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m file_path:\n\u001b[32m---> \u001b[39m\u001b[32m50\u001b[39m     file_name = \u001b[43mos\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m.\u001b[49m\u001b[43mbasename\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile_path\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     51\u001b[39m     class_name = file_name.replace(\u001b[33m'\u001b[39m\u001b[33m.java\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33m'\u001b[39m) \u001b[38;5;28;01mif\u001b[39;00m file_name.endswith(\u001b[33m'\u001b[39m\u001b[33m.java\u001b[39m\u001b[33m'\u001b[39m) \u001b[38;5;28;01melse\u001b[39;00m file_name\n\u001b[32m     53\u001b[39m \u001b[38;5;66;03m# Standardize source node if it's a variable\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m<frozen ntpath>:244\u001b[39m, in \u001b[36mbasename\u001b[39m\u001b[34m(p)\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m<frozen ntpath>:213\u001b[39m, in \u001b[36msplit\u001b[39m\u001b[34m(p)\u001b[39m\n", "\u001b[31mTypeError\u001b[39m: expected str, bytes or os.PathLike object, not float"]}], "source": ["# ========== STAGE 11: STANDARDIZATION AND PROJECT CONNECTION FIXES ==========\n", "\n", "def fix_project_application_connections():\n", "    '''Fix project to have proper connections to both applications'''\n", "    print('🔧 Fixing project-to-application connections...')\n", "    \n", "    # Create proper PROJECT -> APPLICATION connections\n", "    project_connections = [\n", "        {\n", "            'source_node': 'OneInsights',\n", "            'source_type': 'project',\n", "            'destination_node': 'ServiceBolt',\n", "            'destination_type': 'application',\n", "            'relationship': 'contains',\n", "            'file_path': None,\n", "            'application': 'ServiceBolt'\n", "        },\n", "        {\n", "            'source_node': 'OneInsights',\n", "            'source_type': 'project',\n", "            'destination_node': 'UnifiedBolt',\n", "            'destination_type': 'application',\n", "            'relationship': 'contains',\n", "            'file_path': None,\n", "            'application': 'UnifiedBolt'\n", "        }\n", "    ]\n", "    \n", "    print(f'✅ Created {len(project_connections)} project-to-application connections')\n", "    return project_connections\n", "\n", "def standardize_variable_names(df):\n", "    '''Standardize variable names throughout the pipeline'''\n", "    print('🔧 Standardizing variable names...')\n", "    \n", "    standardized_records = []\n", "    variable_mapping = {}  # Track variable name mappings\n", "    \n", "    # Process all records to standardize variable names\n", "    for _, record in df.iterrows():\n", "        new_record = record.copy()\n", "        \n", "        # Get context information\n", "        file_path = record.get('file_path', '')\n", "        app_name = record['application']\n", "        \n", "        # Extract class name from file path\n", "        class_name = 'Unknown'\n", "        if file_path:\n", "            file_name = os.path.basename(file_path)\n", "            class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name\n", "        \n", "        # Standardize source node if it's a variable\n", "        if record['source_type'] == 'variable':\n", "            source_var = record['source_node']\n", "            \n", "            # Check if already standardized\n", "            if '.' not in source_var:\n", "                # Find the method context if available\n", "                method_name = 'field'  # Default to field level\n", "                \n", "                # Look for method context in the same file\n", "                method_records = df[\n", "                    (df['file_path'] == file_path) & \n", "                    (df['destination_type'] == 'method')\n", "                ]\n", "                \n", "                if len(method_records) > 0:\n", "                    # Use the first method found (could be enhanced with better logic)\n", "                    method_name = method_records.iloc[0]['destination_node']\n", "                \n", "                # Create standardized name: ClassName.methodName.variableName\n", "                standardized_name = f\"{class_name}.{method_name}.{source_var}\"\n", "                new_record['source_node'] = standardized_name\n", "                \n", "                # Track the mapping\n", "                variable_mapping[source_var] = standardized_name\n", "        \n", "        # Standardize destination node if it's a variable\n", "        if record['destination_type'] == 'variable':\n", "            dest_var = record['destination_node']\n", "            \n", "            # Check if already standardized\n", "            if '.' not in dest_var:\n", "                # Find the method context if available\n", "                method_name = 'field'  # Default to field level\n", "                \n", "                # Look for method context in the same file\n", "                method_records = df[\n", "                    (df['file_path'] == file_path) & \n", "                    (df['destination_type'] == 'method')\n", "                ]\n", "                \n", "                if len(method_records) > 0:\n", "                    # Use the first method found (could be enhanced with better logic)\n", "                    method_name = method_records.iloc[0]['destination_node']\n", "                \n", "                # Create standardized name: ClassName.methodName.variableName\n", "                standardized_name = f\"{class_name}.{method_name}.{dest_var}\"\n", "                new_record['destination_node'] = standardized_name\n", "                \n", "                # Track the mapping\n", "                variable_mapping[dest_var] = standardized_name\n", "        \n", "        standardized_records.append(new_record.to_dict())\n", "    \n", "    print(f'✅ Standardized {len(variable_mapping)} variable names')\n", "    print(f'📊 Sample mappings: {dict(list(variable_mapping.items())[:5])}')\n", "    \n", "    return pd.DataFrame(standardized_records), variable_mapping\n", "\n", "def create_proper_variable_connections(df, variable_mapping):\n", "    '''Create proper connections for standardized variables'''\n", "    print('🔧 Creating proper variable connections...')\n", "    \n", "    connection_records = []\n", "    \n", "    # For each standardized variable, create proper connections\n", "    for original_var, standardized_var in variable_mapping.items():\n", "        parts = standardized_var.split('.')\n", "        if len(parts) == 3:\n", "            class_name, method_name, var_name = parts\n", "            \n", "            # Find the file path and application for this class\n", "            class_records = df[df['destination_node'] == class_name]\n", "            if len(class_records) > 0:\n", "                class_record = class_records.iloc[0]\n", "                file_path = class_record.get('file_path', '')\n", "                app_name = class_record['application']\n", "                \n", "                # Create class -> variable connection\n", "                connection_records.append({\n", "                    'source_node': class_name,\n", "                    'source_type': 'class',\n", "                    'destination_node': standardized_var,\n", "                    'destination_type': 'variable',\n", "                    'relationship': 'declares_variable',\n", "                    'file_path': file_path,\n", "                    'application': app_name\n", "                })\n", "                \n", "                # Create method -> variable connection if method is not 'field'\n", "                if method_name != 'field':\n", "                    connection_records.append({\n", "                        'source_node': method_name,\n", "                        'source_type': 'method',\n", "                        'destination_node': standardized_var,\n", "                        'destination_type': 'variable',\n", "                        'relationship': 'uses',\n", "                        'file_path': file_path,\n", "                        'application': app_name\n", "                    })\n", "    \n", "    print(f'✅ Created {len(connection_records)} proper variable connections')\n", "    return connection_records\n", "\n", "def apply_standardization_fixes(df):\n", "    '''Apply all standardization fixes'''\n", "    print('\\n🔧 Applying standardization fixes...')\n", "    \n", "    # Fix 1: Project-to-application connections\n", "    project_fixes = fix_project_application_connections()\n", "    \n", "    # Fix 2: Standardize variable names\n", "    df_standardized, variable_mapping = standardize_variable_names(df)\n", "    \n", "    # Fix 3: Create proper variable connections\n", "    variable_connections = create_proper_variable_connections(df_standardized, variable_mapping)\n", "    \n", "    # Combine all fixes\n", "    all_fixes = project_fixes + variable_connections\n", "    df_all_fixes = pd.DataFrame(all_fixes)\n", "    \n", "    # Combine with standardized data\n", "    final_records = df_standardized.to_dict('records') + all_fixes\n", "    \n", "    # Remove duplicates\n", "    seen = set()\n", "    unique_records = []\n", "    \n", "    for record in final_records:\n", "        key = (record['source_node'], record['source_type'], \n", "               record['destination_node'], record['destination_type'], \n", "               record['relationship'])\n", "        if key not in seen:\n", "            seen.add(key)\n", "            unique_records.append(record)\n", "    \n", "    print(f'📊 Removed {len(final_records) - len(unique_records)} duplicates')\n", "    \n", "    return pd.DataFrame(unique_records)\n", "\n", "# Apply standardization fixes\n", "print('🔧 Starting standardization fixes...')\n", "\n", "# Apply fixes to the current final dataset\n", "df_standardized_final = apply_standardization_fixes(df_final_fixed)\n", "\n", "print(f'\\n✅ Standardization complete:')\n", "print(f'   📊 Total relationships: {len(df_standardized_final)}')\n", "if len(df_standardized_final) > 0:\n", "    print(f'   🔗 Relationship types: {df_standardized_final[\"relationship\"].value_counts().to_dict()}')\n", "    print(f'   🏢 Applications: {df_standardized_final[\"application\"].value_counts().to_dict()}')\n", "\n", "# Export standardized dataset\n", "df_standardized_final.to_csv('standardized_final_relationships.csv', index=False)\n", "print(f'✅ Exported standardized dataset to standardized_final_relationships.csv')"]}, {"cell_type": "code", "execution_count": 16, "id": "stage12_final_neo4j_load", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Loading standardized data to Neo4j...\n"]}, {"ename": "NameError", "evalue": "name 'df_standardized_final' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[16]\u001b[39m\u001b[32m, line 150\u001b[39m\n\u001b[32m    148\u001b[39m \u001b[38;5;66;03m# Load standardized data to Neo4j\u001b[39;00m\n\u001b[32m    149\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m🚀 Loading standardized data to Neo4j...\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m150\u001b[39m load_standardized_data_to_neo4j(\u001b[43mdf_standardized_final\u001b[49m)\n\u001b[32m    152\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m🎉 STANDARDIZATION COMPLETE!\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m    153\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m✅ Fixed Issues:\u001b[39m\u001b[33m'\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'df_standardized_final' is not defined"]}], "source": ["# ========== STAGE 12: FINAL NEO4J LOAD WITH STANDARDIZED DATA ==========\n", "\n", "def load_standardized_data_to_neo4j(df_standardized):\n", "    '''Load the standardized data to Neo4j with proper project connections'''\n", "    if graph is None:\n", "        print('❌ Neo4j connection not available')\n", "        return\n", "    \n", "    try:\n", "        # Clear existing data\n", "        print('🧹 Clearing existing data...')\n", "        graph.query('MATCH (n) DETACH DELETE n')\n", "        \n", "        # Load standardized data\n", "        print('📊 Loading standardized data to Neo4j...')\n", "        \n", "        # Node type mapping\n", "        node_type_mapping = {\n", "            'project': 'Project',\n", "            'application': 'Application', \n", "            'folder': 'Folder',\n", "            'file': 'File',\n", "            'class': 'Class',\n", "            'method': 'Method',\n", "            'interface': 'Interface',\n", "            'variable': 'Variable',\n", "            'endpoint': 'Endpoint',\n", "            'data': 'Data'\n", "        }\n", "        \n", "        # Relationship mapping\n", "        relationship_mapping = {\n", "            'contains': 'CONTAINS',\n", "            'declares': 'DECLARES',\n", "            'declares_variable': 'DECLARES_VARIABLE',\n", "            'exposes': 'EXPOSES',\n", "            'extends': 'EXTENDS',\n", "            'implements': 'IMPLEMENTS',\n", "            'has_field': 'HAS_FIELD',\n", "            'uses': 'USES',\n", "            'transforms_to': 'TRANSFORMS_TO',\n", "            'data_find': 'DATA_FIND'\n", "        }\n", "        \n", "        # Load in batches for better performance\n", "        batch_size = 100\n", "        total_batches = len(df_standardized) // batch_size + (1 if len(df_standardized) % batch_size > 0 else 0)\n", "        \n", "        for i in tqdm(range(0, len(df_standardized), batch_size), desc='Loading standardized batches', total=total_batches):\n", "            batch = df_standardized.iloc[i:i+batch_size]\n", "            \n", "            for _, row in batch.iterrows():\n", "                try:\n", "                    source_label = node_type_mapping.get(row['source_type'], row['source_type'].title())\n", "                    target_label = node_type_mapping.get(row['destination_type'], row['destination_type'].title())\n", "                    neo4j_rel_type = relationship_mapping.get(row['relationship'], row['relationship'].upper().replace(' ', '_'))\n", "                    \n", "                    # Create nodes and relationship with additional properties for variables\n", "                    if row['source_type'] == 'variable' or row['destination_type'] == 'variable':\n", "                        # For variables, add context information\n", "                        cypher = f\"\"\"\n", "                        MERGE (source:{source_label} {{name: $source_name, application: $app}})\n", "                        MERGE (target:{target_label} {{name: $target_name, application: $app}})\n", "                        MERGE (source)-[:{neo4j_rel_type}]->(target)\n", "                        \"\"\"\n", "                    else:\n", "                        # Standard relationship\n", "                        cypher = f\"\"\"\n", "                        MERGE (source:{source_label} {{name: $source_name, application: $app}})\n", "                        MERGE (target:{target_label} {{name: $target_name, application: $app}})\n", "                        MERGE (source)-[:{neo4j_rel_type}]->(target)\n", "                        \"\"\"\n", "                    \n", "                    graph.query(cypher, {\n", "                        'source_name': str(row['source_node']),\n", "                        'target_name': str(row['destination_node']),\n", "                        'app': str(row['application'])\n", "                    })\n", "                \n", "                except Exception as e:\n", "                    continue\n", "        \n", "        # Create indexes\n", "        indexes = [\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:File) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Variable) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Data) ON (n.name)'\n", "        ]\n", "        \n", "        for index in indexes:\n", "            try:\n", "                graph.query(index)\n", "            except:\n", "                pass\n", "        \n", "        print('✅ Standardized data successfully loaded to Neo4j')\n", "        \n", "        # Verify project connections\n", "        project_connections = graph.query(\"\"\"\n", "        MATCH (p:Project)-[:CONTAINS]->(a:Application)\n", "        RETURN p.name as project, collect(a.name) as applications\n", "        \"\"\")\n", "        \n", "        print('\\n🏗️ Project-Application Connections:')\n", "        for conn in project_connections:\n", "            print(f'   {conn[\"project\"]} -> {conn[\"applications\"]}')\n", "        \n", "        # Verify variable standardization\n", "        variable_samples = graph.query(\"\"\"\n", "        MATCH (v:Variable)\n", "        WHERE v.name CONTAINS '.'\n", "        RETURN v.name as variable_name\n", "        LIMIT 10\n", "        \"\"\")\n", "        \n", "        print('\\n🔗 Standardized Variable Samples:')\n", "        for var in variable_samples:\n", "            print(f'   {var[\"variable_name\"]}')\n", "        \n", "        # Get final statistics\n", "        stats = graph.query(\"\"\"\n", "        MATCH (n)\n", "        RETURN labels(n)[0] as node_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('\\n📊 Final Standardized Neo4j Node Statistics:')\n", "        for stat in stats:\n", "            print(f'   {stat[\"node_type\"]}: {stat[\"count\"]} nodes')\n", "        \n", "        rel_stats = graph.query(\"\"\"\n", "        MATCH ()-[r]->()\n", "        RETURN type(r) as relationship_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('\\n🔗 Final Standardized Neo4j Relationship Statistics:')\n", "        for stat in rel_stats:\n", "            print(f'   {stat[\"relationship_type\"]}: {stat[\"count\"]} relationships')\n", "    \n", "    except Exception as e:\n", "        print(f'❌ Error loading standardized data to Neo4j: {e}')\n", "\n", "# Load standardized data to Neo4j\n", "print('\\n🚀 Loading standardized data to Neo4j...')\n", "load_standardized_data_to_neo4j(df_standardized_final)\n", "\n", "print('\\n🎉 STANDARDIZATION COMPLETE!')\n", "print('\\n✅ Fixed Issues:')\n", "print('   🏗️ OneInsights project now connects to BOTH applications')\n", "print('   🔗 Variable names standardized as ClassName.methodName.variableName')\n", "print('   🔗 Proper variable-to-class-to-method connections established')\n", "print('   🔗 Complete traceability throughout the pipeline')\n", "print('\\n📁 Final CSV File:')\n", "print('   📄 standardized_final_relationships.csv - Complete standardized dataset')\n", "print('\\n🎯 Now you can track variables like:')\n", "print('   📊 UserController.getUserById.userService')\n", "print('   📊 DataService.processData.resultList')\n", "print('   📊 AuthService.validateToken.tokenData')"]}, {"cell_type": "code", "execution_count": null, "id": "fix_typeerror", "metadata": {}, "outputs": [], "source": ["# ========== FIX FOR TYPEERROR: Handle NaN file_path values ==========\n", "\n", "def standardize_variable_names_fixed(df):\n", "    '''Standardize variable names throughout the pipeline - FIXED VERSION'''\n", "    print('🔧 Standardizing variable names (fixed version)...')\n", "    \n", "    standardized_records = []\n", "    variable_mapping = {}  # Track variable name mappings\n", "    \n", "    # Process all records to standardize variable names\n", "    for _, record in df.iterrows():\n", "        new_record = record.copy()\n", "        \n", "        # Get context information - FIX: Handle NaN values properly\n", "        file_path = record.get('file_path', '')\n", "        app_name = record['application']\n", "        \n", "        # Extract class name from file path - FIX: Handle NaN and non-string values\n", "        class_name = 'Unknown'\n", "        if file_path and pd.notna(file_path) and str(file_path).strip() != '' and str(file_path) != 'nan':\n", "            try:\n", "                file_name = os.path.basename(str(file_path))\n", "                class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name\n", "            except Exception as e:\n", "                print(f'⚠️ Error processing file_path {file_path}: {e}')\n", "                class_name = 'Unknown'\n", "        \n", "        # Standardize source node if it's a variable\n", "        if record['source_type'] == 'variable':\n", "            source_var = record['source_node']\n", "            \n", "            # Check if already standardized\n", "            if '.' not in str(source_var):\n", "                # Find the method context if available\n", "                method_name = 'field'  # Default to field level\n", "                \n", "                # Look for method context in the same file\n", "                if file_path and pd.notna(file_path):\n", "                    method_records = df[\n", "                        (df['file_path'] == file_path) & \n", "                        (df['destination_type'] == 'method')\n", "                    ]\n", "                    \n", "                    if len(method_records) > 0:\n", "                        # Use the first method found\n", "                        method_name = method_records.iloc[0]['destination_node']\n", "                \n", "                # Create standardized name: ClassName.methodName.variableName\n", "                standardized_name = f\"{class_name}.{method_name}.{source_var}\"\n", "                new_record['source_node'] = standardized_name\n", "                \n", "                # Track the mapping\n", "                variable_mapping[source_var] = standardized_name\n", "        \n", "        # Standardize destination node if it's a variable\n", "        if record['destination_type'] == 'variable':\n", "            dest_var = record['destination_node']\n", "            \n", "            # Check if already standardized\n", "            if '.' not in str(dest_var):\n", "                # Find the method context if available\n", "                method_name = 'field'  # Default to field level\n", "                \n", "                # Look for method context in the same file\n", "                if file_path and pd.notna(file_path):\n", "                    method_records = df[\n", "                        (df['file_path'] == file_path) & \n", "                        (df['destination_type'] == 'method')\n", "                    ]\n", "                    \n", "                    if len(method_records) > 0:\n", "                        # Use the first method found\n", "                        method_name = method_records.iloc[0]['destination_node']\n", "                \n", "                # Create standardized name: ClassName.methodName.variableName\n", "                standardized_name = f\"{class_name}.{method_name}.{dest_var}\"\n", "                new_record['destination_node'] = standardized_name\n", "                \n", "                # Track the mapping\n", "                variable_mapping[dest_var] = standardized_name\n", "        \n", "        standardized_records.append(new_record.to_dict())\n", "    \n", "    print(f'✅ Standardized {len(variable_mapping)} variable names')\n", "    print(f'📊 Sample mappings: {dict(list(variable_mapping.items())[:5])}')\n", "    \n", "    return pd.DataFrame(standardized_records), variable_mapping\n", "\n", "def apply_standardization_fixes_fixed(df):\n", "    '''Apply all standardization fixes - FIXED VERSION'''\n", "    print('\\n🔧 Applying standardization fixes (fixed version)...')\n", "    \n", "    # Fix 1: Project-to-application connections\n", "    project_fixes = [\n", "        {\n", "            'source_node': 'OneInsights',\n", "            'source_type': 'project',\n", "            'destination_node': 'ServiceBolt',\n", "            'destination_type': 'application',\n", "            'relationship': 'contains',\n", "            'file_path': None,\n", "            'application': 'ServiceBolt'\n", "        },\n", "        {\n", "            'source_node': 'OneInsights',\n", "            'source_type': 'project',\n", "            'destination_node': 'UnifiedBolt',\n", "            'destination_type': 'application',\n", "            'relationship': 'contains',\n", "            'file_path': None,\n", "            'application': 'UnifiedBolt'\n", "        }\n", "    ]\n", "    \n", "    # Fix 2: Standardize variable names (FIXED)\n", "    df_standardized, variable_mapping = standardize_variable_names_fixed(df)\n", "    \n", "    # Fix 3: Create proper variable connections\n", "    variable_connections = []\n", "    for original_var, standardized_var in variable_mapping.items():\n", "        parts = standardized_var.split('.')\n", "        if len(parts) == 3:\n", "            class_name, method_name, var_name = parts\n", "            \n", "            # Find the file path and application for this class\n", "            class_records = df[df['destination_node'] == class_name]\n", "            if len(class_records) > 0:\n", "                class_record = class_records.iloc[0]\n", "                file_path = class_record.get('file_path', '')\n", "                app_name = class_record['application']\n", "                \n", "                # Create class -> variable connection\n", "                variable_connections.append({\n", "                    'source_node': class_name,\n", "                    'source_type': 'class',\n", "                    'destination_node': standardized_var,\n", "                    'destination_type': 'variable',\n", "                    'relationship': 'declares_variable',\n", "                    'file_path': file_path,\n", "                    'application': app_name\n", "                })\n", "                \n", "                # Create method -> variable connection if method is not 'field'\n", "                if method_name != 'field':\n", "                    variable_connections.append({\n", "                        'source_node': method_name,\n", "                        'source_type': 'method',\n", "                        'destination_node': standardized_var,\n", "                        'destination_type': 'variable',\n", "                        'relationship': 'uses',\n", "                        'file_path': file_path,\n", "                        'application': app_name\n", "                    })\n", "    \n", "    # Combine all fixes\n", "    all_fixes = project_fixes + variable_connections\n", "    \n", "    # Combine with standardized data\n", "    final_records = df_standardized.to_dict('records') + all_fixes\n", "    \n", "    # Remove duplicates\n", "    seen = set()\n", "    unique_records = []\n", "    \n", "    for record in final_records:\n", "        key = (record['source_node'], record['source_type'], \n", "               record['destination_node'], record['destination_type'], \n", "               record['relationship'])\n", "        if key not in seen:\n", "            seen.add(key)\n", "            unique_records.append(record)\n", "    \n", "    print(f'📊 Removed {len(final_records) - len(unique_records)} duplicates')\n", "    \n", "    return pd.DataFrame(unique_records)\n", "\n", "# Apply the FIXED standardization\n", "print('🔧 Applying FIXED standardization...')\n", "try:\n", "    df_standardized_final = apply_standardization_fixes_fixed(df_final_fixed)\n", "    \n", "    print(f'\\n✅ Standardization complete:')\n", "    print(f'   📊 Total relationships: {len(df_standardized_final)}')\n", "    if len(df_standardized_final) > 0:\n", "        print(f'   🔗 Relationship types: {df_standardized_final[\"relationship\"].value_counts().to_dict()}')\n", "        print(f'   🏢 Applications: {df_standardized_final[\"application\"].value_counts().to_dict()}')\n", "    \n", "    # Export standardized dataset\n", "    df_standardized_final.to_csv('standardized_final_relationships_fixed.csv', index=False)\n", "    print(f'✅ Exported standardized dataset to standardized_final_relationships_fixed.csv')\n", "    \n", "except Exception as e:\n", "    print(f'❌ Error in standardization: {e}')\n", "    print('📊 Checking data types in df_final_fixed...')\n", "    if 'df_final_fixed' in globals():\n", "        print(f'   Shape: {df_final_fixed.shape}')\n", "        print(f'   Columns: {df_final_fixed.columns.tolist()}')\n", "        print(f'   file_path column info:')\n", "        print(f'     - Type: {df_final_fixed[\"file_path\"].dtype}')\n", "        print(f'     - Null count: {df_final_fixed[\"file_path\"].isnull().sum()}')\n", "        print(f'     - Sample values: {df_final_fixed[\"file_path\"].head().tolist()}')\n", "    else:\n", "        print('   df_final_fixed not found in globals')"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}