{"cells": [{"cell_type": "code", "execution_count": null, "id": "stage1_setup", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 1: SETUP AND CONFIGURATION ==========\n", "\n", "import os\n", "import pandas as pd\n", "import re\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Tree-sitter for AST parsing\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "# LangChain for LLM processing\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# ========== CONFIGURATION ==========\n", "BASE_PATH = Path(r'C:/Shaik/sample/OneInsights')\n", "NEO4J_URI = 'bolt://localhost:7687'\n", "NEO4J_USER = 'neo4j'\n", "NEO4J_PASSWORD = 'Test@7889'\n", "NEO4J_DB = 'oneinsights-v11'\n", "GOOGLE_API_KEY = 'AIzaSyD29vmAqN-z2ZlJ4vJ5YGEHoQo_79UqXQM'\n", "\n", "# ========== INITIALIZE COMPONENTS ==========\n", "print('🔧 Initializing components...')\n", "\n", "# Neo4j connection\n", "try:\n", "    graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "    print('✅ Neo4j connection established')\n", "except Exception as e:\n", "    print(f'❌ Neo4j connection failed: {e}')\n", "    graph = None\n", "\n", "# Tree-sitter Java parser\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "# LLM for enhanced extraction\n", "llm = ChatGoogleGenerativeAI(\n", "    model='gemini-2.0-flash-exp',\n", "    temperature=0,\n", "    google_api_key=GOOGLE_API_KEY\n", ")\n", "\n", "# ========== APPLICATION MAPPING ==========\n", "APPLICATIONS = {\n", "    'ServiceBolt': 'REST API Service Layer',\n", "    'UnifiedBolt': 'Core Business Logic and Data Layer'\n", "}\n", "\n", "# ========== NOISE FILTERING ==========\n", "NOISE_VARIABLES = {\n", "    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',\n", "    'temp', 'tmp', 'obj', 'item', 'elem', 'node', 'val', 'value',\n", "    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',\n", "    'this', 'super', 'null', 'true', 'false', 'void', 'return',\n", "    'it', 'ex', 'e1', 'e2', 'o1', 'o2'\n", "}\n", "\n", "def is_meaningful_variable(var_name):\n", "    '''Filter meaningful variables only'''\n", "    if not var_name or len(var_name) < 3:\n", "        return False\n", "    if var_name.lower() in NOISE_VARIABLES:\n", "        return False\n", "    if re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', var_name):\n", "        return True\n", "    return False\n", "\n", "# ========== GLOBAL DATA STRUCTURES ==========\n", "all_relationships = []  # Master list of all relationships\n", "class_registry = {}     # Track all classes and their details\n", "method_registry = {}    # Track all methods and their details\n", "variable_registry = {}  # Track all variables and their context\n", "\n", "print('🚀 Setup complete! Ready for clean v11 analysis...')"]}, {"cell_type": "code", "execution_count": null, "id": "stage2_hierarchy", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 2: FILE DISCOVERY AND HIERARCHY BUILDING ==========\n", "\n", "def build_project_hierarchy():\n", "    '''Build complete project hierarchy: PROJECT → APPLICATION → FOLDER → FILE'''\n", "    print('🏗️ Building project hierarchy...')\n", "    \n", "    hierarchy_relationships = []\n", "    \n", "    # PROJECT level\n", "    project_name = 'OneInsights'\n", "    \n", "    # APPLICATION level - Connect project to BOTH applications\n", "    for app_name in APPLICATIONS.keys():\n", "        hierarchy_relationships.append({\n", "            'source_node': project_name,\n", "            'source_type': 'project',\n", "            'destination_node': app_name,\n", "            'destination_type': 'application',\n", "            'relationship': 'contains',\n", "            'file_path': None,\n", "            'application': app_name\n", "        })\n", "    \n", "    # FOLDER and FILE level\n", "    for app_name in APPLICATIONS.keys():\n", "        app_path = BASE_PATH / app_name\n", "        if not app_path.exists():\n", "            print(f'⚠️ Application path not found: {app_path}')\n", "            continue\n", "        \n", "        print(f'📁 Processing application: {app_name}')\n", "        \n", "        # Find all Java files\n", "        java_files = list(app_path.rglob('*.java'))\n", "        print(f'   Found {len(java_files)} Java files')\n", "        \n", "        folders_processed = set()\n", "        \n", "        for java_file in java_files:\n", "            relative_path = java_file.relative_to(app_path)\n", "            path_parts = relative_path.parts\n", "            \n", "            # Build folder hierarchy\n", "            current_parent = app_name\n", "            current_parent_type = 'application'\n", "            \n", "            # Process each folder in the path\n", "            for i, folder in enumerate(path_parts[:-1]):  # Exclude the file itself\n", "                folder_key = '/'.join(path_parts[:i+1])\n", "                \n", "                if folder_key not in folders_processed:\n", "                    # APPLICATION/FOLDER → FOLDER relationship\n", "                    hierarchy_relationships.append({\n", "                        'source_node': current_parent,\n", "                        'source_type': current_parent_type,\n", "                        'destination_node': folder,\n", "                        'destination_type': 'folder',\n", "                        'relationship': 'contains',\n", "                        'file_path': str(java_file.parent),\n", "                        'application': app_name\n", "                    })\n", "                    folders_processed.add(folder_key)\n", "                \n", "                current_parent = folder\n", "                current_parent_type = 'folder'\n", "            \n", "            # FOLDER → FILE relationship\n", "            file_name = java_file.stem  # Without .java extension\n", "            hierarchy_relationships.append({\n", "                'source_node': current_parent,\n", "                'source_type': current_parent_type,\n", "                'destination_node': file_name,\n", "                'destination_type': 'file',\n", "                'relationship': 'contains',\n", "                'file_path': str(java_file),\n", "                'application': app_name\n", "            })\n", "    \n", "    print(f'✅ Built hierarchy with {len(hierarchy_relationships)} relationships')\n", "    return hierarchy_relationships\n", "\n", "# Build the hierarchy\n", "hierarchy_rels = build_project_hierarchy()\n", "all_relationships.extend(hierarchy_rels)\n", "\n", "print(f'📊 Current total relationships: {len(all_relationships)}')"]}, {"cell_type": "code", "execution_count": null, "id": "stage3_ast_analysis", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 3: AST-BASED CODE ANALYSIS ==========\n", "\n", "def extract_class_info(node, source_code):\n", "    '''Extract class information from AST node'''\n", "    class_name = None\n", "    extends_class = None\n", "    \n", "    for child in node.children:\n", "        if child.type == 'identifier':\n", "            class_name = source_code[child.start_byte:child.end_byte]\n", "        elif child.type == 'superclass':\n", "            for subchild in child.children:\n", "                if subchild.type == 'type_identifier':\n", "                    extends_class = source_code[subchild.start_byte:subchild.end_byte]\n", "    \n", "    return class_name, extends_class\n", "\n", "def extract_method_info(node, source_code):\n", "    '''Extract method information from AST node'''\n", "    method_name = None\n", "    parameters = []\n", "    \n", "    for child in node.children:\n", "        if child.type == 'identifier':\n", "            method_name = source_code[child.start_byte:child.end_byte]\n", "        elif child.type == 'formal_parameters':\n", "            for param_child in child.children:\n", "                if param_child.type == 'formal_parameter':\n", "                    for param_part in param_child.children:\n", "                        if param_part.type == 'variable_declarator':\n", "                            for var_part in param_part.children:\n", "                                if var_part.type == 'identifier':\n", "                                    param_name = source_code[var_part.start_byte:var_part.end_byte]\n", "                                    if is_meaningful_variable(param_name):\n", "                                        parameters.append(param_name)\n", "    \n", "    return method_name, parameters\n", "\n", "def extract_variables_from_node(node, source_code, method_context=None):\n", "    '''Extract variables from any AST node'''\n", "    variables = []\n", "    \n", "    def traverse_node(n):\n", "        if n.type == 'variable_declarator':\n", "            for child in n.children:\n", "                if child.type == 'identifier':\n", "                    var_name = source_code[child.start_byte:child.end_byte]\n", "                    if is_meaningful_variable(var_name):\n", "                        variables.append(var_name)\n", "        \n", "        for child in n.children:\n", "            traverse_node(child)\n", "    \n", "    traverse_node(node)\n", "    return variables\n", "\n", "def analyze_java_file_ast(file_path, app_name):\n", "    '''Analyze Java file using AST and extract all relationships'''\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            source_code = f.read()\n", "    except Exception as e:\n", "        print(f'❌ Error reading {file_path}: {e}')\n", "        return []\n", "    \n", "    tree = parser.parse(bytes(source_code, 'utf8'))\n", "    relationships = []\n", "    \n", "    file_name = Path(file_path).stem\n", "    current_class = None\n", "    current_method = None\n", "    \n", "    def traverse_ast(node, depth=0):\n", "        nonlocal current_class, current_method\n", "        \n", "        if node.type == 'class_declaration':\n", "            class_name, extends_class = extract_class_info(node, source_code)\n", "            if class_name:\n", "                current_class = class_name\n", "                \n", "                # Register class\n", "                class_registry[class_name] = {\n", "                    'file_path': file_path,\n", "                    'application': app_name,\n", "                    'extends': extends_class\n", "                }\n", "                \n", "                # FILE → CLASS relationship\n", "                relationships.append({\n", "                    'source_node': file_name,\n", "                    'source_type': 'file',\n", "                    'destination_node': class_name,\n", "                    'destination_type': 'class',\n", "                    'relationship': 'contains',\n", "                    'file_path': file_path,\n", "                    'application': app_name\n", "                })\n", "                \n", "                # CLASS → EXTENDS relationship\n", "                if extends_class:\n", "                    relationships.append({\n", "                        'source_node': class_name,\n", "                        'source_type': 'class',\n", "                        'destination_node': extends_class,\n", "                        'destination_type': 'class',\n", "                        'relationship': 'extends',\n", "                        'file_path': file_path,\n", "                        'application': app_name\n", "                    })\n", "        \n", "        elif node.type == 'method_declaration' and current_class:\n", "            method_name, parameters = extract_method_info(node, source_code)\n", "            if method_name:\n", "                current_method = method_name\n", "                \n", "                # Register method\n", "                method_key = f\"{current_class}.{method_name}\"\n", "                method_registry[method_key] = {\n", "                    'class': current_class,\n", "                    'file_path': file_path,\n", "                    'application': app_name,\n", "                    'parameters': parameters\n", "                }\n", "                \n", "                # CLASS → METHOD relationship\n", "                relationships.append({\n", "                    'source_node': current_class,\n", "                    'source_type': 'class',\n", "                    'destination_node': method_name,\n", "                    'destination_type': 'method',\n", "                    'relationship': 'declares',\n", "                    'file_path': file_path,\n", "                    'application': app_name\n", "                })\n", "                \n", "                # Extract variables from method\n", "                method_variables = extract_variables_from_node(node, source_code, method_name)\n", "                for var_name in method_variables:\n", "                    # Create standardized variable name\n", "                    standardized_var = f\"{current_class}.{method_name}.{var_name}\"\n", "                    \n", "                    # Register variable\n", "                    variable_registry[standardized_var] = {\n", "                        'original_name': var_name,\n", "                        'class': current_class,\n", "                        'method': method_name,\n", "                        'file_path': file_path,\n", "                        'application': app_name\n", "                    }\n", "                    \n", "                    # METHOD → VARIABLE relationship\n", "                    relationships.append({\n", "                        'source_node': method_name,\n", "                        'source_type': 'method',\n", "                        'destination_node': standardized_var,\n", "                        'destination_type': 'variable',\n", "                        'relationship': 'uses',\n", "                        'file_path': file_path,\n", "                        'application': app_name\n", "                    })\n", "        \n", "        # Continue traversing\n", "        for child in node.children:\n", "            traverse_ast(child, depth + 1)\n", "    \n", "    traverse_ast(tree.root_node)\n", "    return relationships\n", "\n", "# Process all Java files\n", "print('🔍 Analyzing Java files with AST...')\n", "ast_relationships = []\n", "\n", "for app_name in APPLICATIONS.keys():\n", "    app_path = BASE_PATH / app_name\n", "    if app_path.exists():\n", "        java_files = list(app_path.rglob('*.java'))\n", "        print(f'📁 Processing {len(java_files)} files in {app_name}...')\n", "        \n", "        for java_file in tqdm(java_files, desc=f'AST Analysis - {app_name}'):\n", "            file_rels = analyze_java_file_ast(java_file, app_name)\n", "            ast_relationships.extend(file_rels)\n", "\n", "all_relationships.extend(ast_relationships)\n", "print(f'✅ AST analysis complete. Added {len(ast_relationships)} relationships')\n", "print(f'📊 Total relationships: {len(all_relationships)}')\n", "print(f'📊 Registered: {len(class_registry)} classes, {len(method_registry)} methods, {len(variable_registry)} variables')"]}, {"cell_type": "code", "execution_count": null, "id": "stage4_variable_standardization", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 4: VARIABLE STANDARDIZATION AND CONNECTION BUILDING ==========\n", "\n", "def create_variable_transformations():\n", "    '''Create variable transformation relationships based on method flow'''\n", "    print('🔗 Creating variable transformations...')\n", "    \n", "    transformation_relationships = []\n", "    \n", "    # Group variables by class and method\n", "    method_variables = defaultdict(list)\n", "    for var_name, var_info in variable_registry.items():\n", "        method_key = f\"{var_info['class']}.{var_info['method']}\"\n", "        method_variables[method_key].append(var_name)\n", "    \n", "    # Create transformations within methods\n", "    for method_key, variables in method_variables.items():\n", "        if len(variables) > 1:\n", "            # Create transformation chain\n", "            for i in range(len(variables) - 1):\n", "                source_var = variables[i]\n", "                target_var = variables[i + 1]\n", "                \n", "                var_info = variable_registry[source_var]\n", "                \n", "                transformation_relationships.append({\n", "                    'source_node': source_var,\n", "                    'source_type': 'variable',\n", "                    'destination_node': target_var,\n", "                    'destination_type': 'variable',\n", "                    'relationship': 'transforms_to',\n", "                    'file_path': var_info['file_path'],\n", "                    'application': var_info['application']\n", "                })\n", "    \n", "    print(f'✅ Created {len(transformation_relationships)} variable transformations')\n", "    return transformation_relationships\n", "\n", "def create_class_variable_connections():\n", "    '''Create CLASS → VARIABLE relationships for all standardized variables'''\n", "    print('🔗 Creating class-variable connections...')\n", "    \n", "    class_var_relationships = []\n", "    \n", "    for var_name, var_info in variable_registry.items():\n", "        class_var_relationships.append({\n", "            'source_node': var_info['class'],\n", "            'source_type': 'class',\n", "            'destination_node': var_name,\n", "            'destination_type': 'variable',\n", "            'relationship': 'declares_variable',\n", "            'file_path': var_info['file_path'],\n", "            'application': var_info['application']\n", "        })\n", "    \n", "    print(f'✅ Created {len(class_var_relationships)} class-variable connections')\n", "    return class_var_relationships\n", "\n", "def create_cross_class_connections():\n", "    '''Create connections between classes that use similar variables'''\n", "    print('🔗 Creating cross-class connections...')\n", "    \n", "    cross_connections = []\n", "    \n", "    # Group variables by original name\n", "    original_name_groups = defaultdict(list)\n", "    for var_name, var_info in variable_registry.items():\n", "        original_name_groups[var_info['original_name']].append((var_name, var_info))\n", "    \n", "    # Create connections between classes that use the same variable names\n", "    for original_name, var_list in original_name_groups.items():\n", "        if len(var_list) > 1:\n", "            # Create data flow connections\n", "            for i in range(len(var_list) - 1):\n", "                source_var, source_info = var_list[i]\n", "                target_var, target_info = var_list[i + 1]\n", "                \n", "                if source_info['class'] != target_info['class']:\n", "                    cross_connections.append({\n", "                        'source_node': source_var,\n", "                        'source_type': 'variable',\n", "                        'destination_node': target_var,\n", "                        'destination_type': 'variable',\n", "                        'relationship': 'data_find',\n", "                        'file_path': source_info['file_path'],\n", "                        'application': source_info['application']\n", "                    })\n", "    \n", "    print(f'✅ Created {len(cross_connections)} cross-class connections')\n", "    return cross_connections\n", "\n", "# Create all variable-related relationships\n", "var_transformations = create_variable_transformations()\n", "class_var_connections = create_class_variable_connections()\n", "cross_class_connections = create_cross_class_connections()\n", "\n", "# Add to master list\n", "all_relationships.extend(var_transformations)\n", "all_relationships.extend(class_var_connections)\n", "all_relationships.extend(cross_class_connections)\n", "\n", "print(f'✅ Variable standardization complete')\n", "print(f'📊 Total relationships: {len(all_relationships)}')"]}, {"cell_type": "code", "execution_count": null, "id": "stage4b_llm_preparation", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 4B: LLM PREPARATION WITH CURRENT CONTEXT ==========\n", "\n", "def build_focused_system_prompt_v11(file_path, current_relationships, class_registry):\n", "    '''Build system prompt with current AST context for LLM'''\n", "    \n", "    # Filter relationships for this specific file\n", "    file_relationships = [r for r in current_relationships if r.get('file_path') == file_path]\n", "    \n", "    # Build AST context from current relationships\n", "    ast_context = 'CURRENT AST RELATIONSHIPS:\\n'\n", "    for rel in file_relationships[:20]:  # Limit to avoid token overflow\n", "        ast_context += f\"{rel['source_type']}:{rel['source_node']} -[{rel['relationship']}]-> {rel['destination_type']}:{rel['destination_node']}\\n\"\n", "    \n", "    # Build class registry context\n", "    registry_context = 'KNOWN CLASSES:\\n'\n", "    for class_name, info in class_registry.items():\n", "        registry_context += f'- {class_name} (app: {info[\"application\"]})\\n'\n", "    \n", "    # Build method context\n", "    method_context = 'KNOWN METHODS:\\n'\n", "    for method_key, info in method_registry.items():\n", "        method_context += f'- {method_key} (class: {info[\"class\"]})\\n'\n", "    \n", "    prompt = f\"\"\"\n", "You are a Java code lineage extraction engine. Extract relationships between code entities using CURRENT CONTEXT:\n", "\n", "CURRENT CONTEXT:\n", "{registry_context}\n", "\n", "{method_context}\n", "\n", "{ast_context}\n", "\n", "CRITICAL RULES - FOCUSED EXTRACTION:\n", "1. Use SIMPLE names only (remove prefixes like \"method:\", \"class:\", etc.)\n", "2. MANDATORY RELATIONSHIP DIRECTIONS:\n", "   - class -[declares]-> method  \n", "   - class -[declares_variable]-> variable\n", "   - class -[exposes]-> endpoint\n", "   - class -[extends]-> class (prioritized)\n", "   - class -[data_find]-> data\n", "   - method -[uses]-> variable\n", "   - variable -[transforms_to]-> variable (meaningful only)\n", "3. Extract REST API endpoints as 'endpoint' nodes (GET /api/users, POST /api/data)\n", "4. Extract database tables/entities as 'data' nodes\n", "5. Focus on MEANINGFUL variables only (length > 3 or business-relevant)\n", "6. Prioritize EXTENDS over IMPLEMENTS relationships\n", "7. Use standardized variable format: ClassName.methodName.variableName\n", "8. Clean node names (remove \"method:\", \"class:\" prefixes)\n", "\n", "Extract triples in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the triples, no explanations.\n", "\"\"\"\n", "    return prompt\n", "\n", "def extract_enhanced_variable_lineage_v11(source_code_str, class_name, method_context=None):\n", "    '''Extract variable lineage with standardized naming'''\n", "    variable_lineage = []\n", "    \n", "    # Enhanced variable assignment patterns\n", "    assignment_patterns = [\n", "        # Method return assignments: result = methodName(...)\n", "        r'(\\w{3,})\\s*=\\s*(\\w{3,})\\s*\\(.*?\\)',\n", "        # Service/Repository calls: result = serviceInstance.methodName(...)\n", "        r'(\\w{3,})\\s*=\\s*(\\w{3,})\\s*\\.\\s*(\\w+)\\s*\\(',\n", "        # Object creation: result = new ClassName(...)\n", "        r'(\\w{3,})\\s*=\\s*new\\s+(\\w+)\\s*\\(',\n", "        # Field access: result = object.fieldName\n", "        r'(\\w{3,})\\s*=\\s*(\\w{3,})\\s*\\.\\s*(\\w+)(?!\\s*\\()',\n", "        # Simple assignments: result = variable\n", "        r'(\\w{3,})\\s*=\\s*(\\w{3,})(?!\\s*[\\(\\.])',\n", "    ]\n", "    \n", "    for pattern in assignment_patterns:\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            if len(match) >= 2:\n", "                result_var = match[0]\n", "                source_identifier = match[1]\n", "                \n", "                if (is_meaningful_variable(result_var) and \n", "                    is_meaningful_variable(source_identifier) and\n", "                    result_var != source_identifier):\n", "                    \n", "                    # Create standardized variable names\n", "                    method_name = method_context or 'field'\n", "                    result_standardized = f\"{class_name}.{method_name}.{result_var}\"\n", "                    source_standardized = f\"{class_name}.{method_name}.{source_identifier}\"\n", "                    \n", "                    variable_lineage.append({\n", "                        'source_node': source_standardized,\n", "                        'source_type': 'variable',\n", "                        'destination_node': result_standardized,\n", "                        'destination_type': 'variable',\n", "                        'relationship': 'transforms_to',\n", "                        'file_path': None,  # Will be set by caller\n", "                        'application': None  # Will be set by caller\n", "                    })\n", "    \n", "    return variable_lineage\n", "\n", "# Prepare documents for LLM processing\n", "print('📄 Preparing documents for LLM processing...')\n", "\n", "splitter = RecursiveCharacterTextSplitter.from_language(\n", "    language=LC_Language.JAVA,\n", "    chunk_size=4000,\n", "    chunk_overlap=200\n", ")\n", "\n", "java_docs, split_docs = [], []\n", "\n", "for app_name in APPLICATIONS.keys():\n", "    app_path = BASE_PATH / app_name\n", "    if app_path.exists():\n", "        for java_file in app_path.rglob('*.java'):\n", "            try:\n", "                loader = TextLoader(str(java_file), encoding='utf-8')\n", "                docs = loader.load()\n", "                java_docs.extend(docs)\n", "            except Exception as e:\n", "                continue\n", "\n", "for doc in java_docs:\n", "    split_docs.extend(splitter.split_documents([doc]))\n", "\n", "print(f'📄 Prepared {len(split_docs)} document chunks for LLM processing')"]}, {"cell_type": "code", "execution_count": null, "id": "stage4c_llm_processing", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 4C: LLM PROCESSING WITH CURRENT CONTEXT ==========\n", "\n", "def normalize_entity_v11(entity_name, entity_type):\n", "    '''Normalize entity names for consistency'''\n", "    if not entity_name:\n", "        return entity_name\n", "    \n", "    # Remove prefixes\n", "    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "    for prefix in prefixes:\n", "        if entity_name.lower().startswith(prefix):\n", "            entity_name = entity_name[len(prefix):]\n", "    \n", "    # Remove file extensions\n", "    entity_name = re.sub(r'\\.(java|class)$', '', entity_name, flags=re.IGNORECASE)\n", "    \n", "    # Clean dots for class names (but preserve for standardized variables)\n", "    if entity_type in ['class', 'method'] and '.' in entity_name and not entity_type == 'variable':\n", "        entity_name = entity_name.split('.')[-1]\n", "    \n", "    # Match with class registry for consistency\n", "    if entity_type == 'class':\n", "        for class_name in class_registry.keys():\n", "            if entity_name.lower() == class_name.lower():\n", "                return class_name\n", "    \n", "    return entity_name\n", "\n", "def extract_method_variable_context_v11(source_code, class_name):\n", "    '''Extract variables with method context using current approach'''\n", "    method_variables = []\n", "    \n", "    # Find method declarations\n", "    method_pattern = r'(?:public|private|protected)?\\s*(?:static)?\\s*(?:\\w+\\s+)*(\\w+)\\s*\\([^)]*\\)\\s*\\{([^}]*(?:\\{[^}]*\\}[^}]*)*)\\}'\n", "    method_matches = re.findall(method_pattern, source_code, re.DOTALL)\n", "    \n", "    for method_name, method_body in method_matches:\n", "        if is_meaningful_variable(method_name):\n", "            # Extract variable lineage within this method\n", "            method_var_lineage = extract_enhanced_variable_lineage_v11(method_body, class_name, method_name)\n", "            method_variables.extend(method_var_lineage)\n", "    \n", "    return method_variables\n", "\n", "# Initialize LLM lineage collection\n", "all_llm_lineage = []\n", "\n", "print('🤖 Starting LLM extraction with current context...')\n", "\n", "for chunk in tqdm(split_docs, desc='LLM Processing'):\n", "    file_path = chunk.metadata.get('source')\n", "    \n", "    # Build system prompt using current relationships\n", "    system_prompt = build_focused_system_prompt_v11(file_path, all_relationships, class_registry)\n", "    \n", "    # Extract class name from file path for context\n", "    file_name = os.path.basename(file_path) if file_path else 'unknown'\n", "    class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name\n", "    \n", "    # Extract enhanced variable lineage with method context\n", "    enhanced_var_lineage = extract_method_variable_context_v11(chunk.page_content, class_name)\n", "    \n", "    # Set file_path and application for enhanced variables\n", "    app_name = 'ServiceBolt' if 'ServiceBolt' in str(file_path) else 'UnifiedBolt'\n", "    for var_rel in enhanced_var_lineage:\n", "        var_rel['file_path'] = file_path\n", "        var_rel['application'] = app_name\n", "    \n", "    all_llm_lineage.extend(enhanced_var_lineage)\n", "    \n", "    # LLM Graph Transformer with current context\n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=system_prompt,\n", "        allowed_nodes=['project', 'application', 'folder', 'file', 'class', 'method', 'interface', 'variable', 'endpoint', 'data'],\n", "        allowed_relationships=[\n", "            ('project', 'contains', 'application'),\n", "            ('application', 'contains', 'folder'),\n", "            ('folder', 'contains', 'folder'),\n", "            ('folder', 'contains', 'file'),\n", "            ('file', 'declares', 'class'),\n", "            ('file', 'declares', 'interface'),\n", "            ('class', 'declares', 'method'),\n", "            ('class', 'declares_variable', 'variable'),\n", "            ('class', 'exposes', 'endpoint'),\n", "            ('class', 'extends', 'class'),\n", "            ('class', 'implements', 'interface'),\n", "            ('class', 'has_field', 'variable'),\n", "            ('method', 'uses', 'variable'),\n", "            ('variable', 'transforms_to', 'variable'),\n", "            ('class', 'data_find', 'data'),\n", "            ('method', 'data_find', 'data'),\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False,\n", "    )\n", "    \n", "    try:\n", "        graph_docs = transformer.convert_to_graph_documents([chunk])\n", "        for gd in graph_docs:\n", "            for rel in gd.relationships:\n", "                s_node = rel.source.id.strip()\n", "                s_type = rel.source.type.strip().lower()\n", "                t_node = rel.target.id.strip()\n", "                t_type = rel.target.type.strip().lower()\n", "                rel_type = rel.type.strip().lower()\n", "\n", "                # Normalize entities\n", "                s_node = normalize_entity_v11(s_node, s_type)\n", "                t_node = normalize_entity_v11(t_node, t_type)\n", "                \n", "                # Skip if empty or invalid\n", "                if not s_node or not t_node or s_node == t_node:\n", "                    continue\n", "                \n", "                # Determine application\n", "                app_name = 'ServiceBolt' if 'ServiceBolt' in str(file_path) else 'UnifiedBolt'\n", "                \n", "                # Standardize variable names if they're variables\n", "                if s_type == 'variable' and '.' not in s_node:\n", "                    s_node = f\"{class_name}.field.{s_node}\"\n", "                if t_type == 'variable' and '.' not in t_node:\n", "                    t_node = f\"{class_name}.field.{t_node}\"\n", "                \n", "                all_llm_lineage.append({\n", "                    'source_node': s_node,\n", "                    'source_type': s_type,\n", "                    'destination_node': t_node,\n", "                    'destination_type': t_type,\n", "                    'relationship': rel_type,\n", "                    'file_path': file_path,\n", "                    'application': app_name\n", "                })\n", "    except Exception as e:\n", "        print(f'⚠️ LLM processing error for {file_path}: {e}')\n", "        continue\n", "\n", "# Add LLM relationships to master list\n", "all_relationships.extend(all_llm_lineage)\n", "\n", "print(f'✅ LLM processing complete. Added {len(all_llm_lineage)} relationships')\n", "print(f'📊 Total relationships: {len(all_relationships)}')\n", "\n", "if len(all_llm_lineage) > 0:\n", "    llm_df = pd.DataFrame(all_llm_lineage)\n", "    print(f'   🔗 LLM relationship types: {llm_df[\"relationship\"].value_counts().to_dict()}')\n", "    print(f'   🏢 LLM applications: {llm_df[\"application\"].value_counts().to_dict()}')"]}, {"cell_type": "code", "execution_count": null, "id": "stage5_data_cleaning", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 5: DATA CLEANING AND VALIDATION ==========\n", "\n", "def clean_and_validate_data():\n", "    '''Clean, validate, and prepare data for Neo4j'''\n", "    print('🧹 Cleaning and validating data...')\n", "    \n", "    # Convert to DataFrame for easier processing\n", "    df = pd.DataFrame(all_relationships)\n", "    \n", "    print(f'📊 Initial dataset: {len(df)} relationships')\n", "    \n", "    # 1. <PERSON><PERSON> missing values\n", "    df['file_path'] = df['file_path'].fillna('')\n", "    df['application'] = df['application'].fillna('Unknown')\n", "    \n", "    # 2. <PERSON><PERSON><PERSON> duplicates\n", "    initial_count = len(df)\n", "    df = df.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])\n", "    print(f'🔄 Removed {initial_count - len(df)} duplicates')\n", "    \n", "    # 3. Validate node names\n", "    def is_valid_node_name(name):\n", "        if pd.isna(name) or str(name).strip() == '':\n", "            return False\n", "        return True\n", "    \n", "    valid_mask = (df['source_node'].apply(is_valid_node_name) & \n", "                  df['destination_node'].apply(is_valid_node_name))\n", "    \n", "    invalid_count = len(df) - valid_mask.sum()\n", "    if invalid_count > 0:\n", "        print(f'🔄 Removed {invalid_count} relationships with invalid node names')\n", "        df = df[valid_mask]\n", "    \n", "    # 4. Standardize relationship types\n", "    relationship_mapping = {\n", "        'contains': 'CONTAINS',\n", "        'declares': 'DECLARES',\n", "        'declares_variable': 'DECLARES_VARIABLE',\n", "        'extends': 'EXTENDS',\n", "        'uses': 'USES',\n", "        'transforms_to': 'TRANSFORMS_TO',\n", "        'data_find': 'DATA_FIND'\n", "    }\n", "    \n", "    df['neo4j_relationship'] = df['relationship'].map(relationship_mapping)\n", "    df['neo4j_relationship'] = df['neo4j_relationship'].fillna(df['relationship'].str.upper())\n", "    \n", "    # 5. Add node labels for Neo4j\n", "    node_type_mapping = {\n", "        'project': 'Project',\n", "        'application': 'Application',\n", "        'folder': 'Folder',\n", "        'file': 'File',\n", "        'class': 'Class',\n", "        'method': 'Method',\n", "        'variable': 'Variable'\n", "    }\n", "    \n", "    df['source_label'] = df['source_type'].map(node_type_mapping)\n", "    df['destination_label'] = df['destination_type'].map(node_type_mapping)\n", "    \n", "    # 6. Generate statistics\n", "    print('\\n📊 Final Dataset Statistics:')\n", "    print(f'   Total relationships: {len(df)}')\n", "    print(f'   Relationship types: {df[\"relationship\"].value_counts().to_dict()}')\n", "    print(f'   Applications: {df[\"application\"].value_counts().to_dict()}')\n", "    print(f'   Node types: {df[\"source_type\"].value_counts().to_dict()}')\n", "    \n", "    # 7. Validate standardized variables\n", "    standardized_vars = df[df['destination_type'] == 'variable']['destination_node']\n", "    properly_standardized = standardized_vars.str.contains(r'^[^.]+\\.[^.]+\\.[^.]+$', na=False)\n", "    print(f'   Properly standardized variables: {properly_standardized.sum()}/{len(standardized_vars)}')\n", "    \n", "    # 8. Export cleaned data\n", "    df.to_csv('oneinsights_v11_clean_relationships.csv', index=False)\n", "    print(f'✅ Exported clean dataset to oneinsights_v11_clean_relationships.csv')\n", "    \n", "    return df\n", "\n", "# Clean the data\n", "df_clean = clean_and_validate_data()"]}, {"cell_type": "code", "execution_count": null, "id": "stage6_neo4j_loading", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 6: NEO4J LOADING WITH PROPER STRUCTURE ==========\n", "\n", "def load_to_neo4j(df):\n", "    '''Load clean data to Neo4j with proper structure'''\n", "    if graph is None:\n", "        print('❌ Neo4j connection not available')\n", "        return\n", "    \n", "    try:\n", "        print('🚀 Loading data to Neo4j...')\n", "        \n", "        # Clear existing data\n", "        print('🧹 Clearing existing data...')\n", "        graph.query('MATCH (n) DETACH DELETE n')\n", "        \n", "        # Load data in batches\n", "        batch_size = 100\n", "        total_batches = len(df) // batch_size + (1 if len(df) % batch_size > 0 else 0)\n", "        \n", "        print(f'📊 Loading {len(df)} relationships in {total_batches} batches...')\n", "        \n", "        for i in tqdm(range(0, len(df), batch_size), desc='Loading to Neo4j', total=total_batches):\n", "            batch = df.iloc[i:i+batch_size]\n", "            \n", "            for _, row in batch.iterrows():\n", "                try:\n", "                    # Create Cypher query\n", "                    cypher = f\"\"\"\n", "                    MERGE (source:{row['source_label']} {{name: $source_name, application: $app}})\n", "                    MERGE (target:{row['destination_label']} {{name: $target_name, application: $app}})\n", "                    MERGE (source)-[:{row['neo4j_relationship']}]->(target)\n", "                    \"\"\"\n", "                    \n", "                    # Execute query\n", "                    graph.query(cypher, {\n", "                        'source_name': str(row['source_node']),\n", "                        'target_name': str(row['destination_node']),\n", "                        'app': str(row['application'])\n", "                    })\n", "                \n", "                except Exception as e:\n", "                    print(f'⚠️ Error loading relationship: {e}')\n", "                    continue\n", "        \n", "        # Create indexes for better performance\n", "        print('🔧 Creating indexes...')\n", "        indexes = [\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:File) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Variable) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Folder) ON (n.name)'\n", "        ]\n", "        \n", "        for index in indexes:\n", "            try:\n", "                graph.query(index)\n", "            except Exception as e:\n", "                print(f'⚠️ Index creation warning: {e}')\n", "        \n", "        print('✅ Data successfully loaded to Neo4j!')\n", "        \n", "        # Verify the load\n", "        verify_neo4j_load()\n", "        \n", "    except Exception as e:\n", "        print(f'❌ Error loading to Neo4j: {e}')\n", "\n", "def verify_neo4j_load():\n", "    '''Verify the Neo4j load and show statistics'''\n", "    print('\\n🔍 Verifying Neo4j load...')\n", "    \n", "    try:\n", "        # Node statistics\n", "        node_stats = graph.query(\"\"\"\n", "        MATCH (n)\n", "        RETURN labels(n)[0] as node_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('📊 Node Statistics:')\n", "        for stat in node_stats:\n", "            print(f'   {stat[\"node_type\"]}: {stat[\"count\"]} nodes')\n", "        \n", "        # Relationship statistics\n", "        rel_stats = graph.query(\"\"\"\n", "        MATCH ()-[r]->()\n", "        RETURN type(r) as relationship_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('\\n🔗 Relationship Statistics:')\n", "        for stat in rel_stats:\n", "            print(f'   {stat[\"relationship_type\"]}: {stat[\"count\"]} relationships')\n", "        \n", "        # Verify project structure\n", "        project_structure = graph.query(\"\"\"\n", "        MATCH (p:Project)-[:CONTAINS]->(a:Application)\n", "        RETURN p.name as project, collect(a.name) as applications\n", "        \"\"\")\n", "        \n", "        print('\\n🏗️ Project Structure:')\n", "        for struct in project_structure:\n", "            print(f'   {struct[\"project\"]} → {struct[\"applications\"]}')\n", "        \n", "        # Sample standardized variables\n", "        sample_vars = graph.query(\"\"\"\n", "        MATCH (v:Variable)\n", "        WHERE v.name CONTAINS '.'\n", "        RETURN v.name as variable_name\n", "        LIMIT 10\n", "        \"\"\")\n", "        \n", "        print('\\n🔗 Sample Standardized Variables:')\n", "        for var in sample_vars:\n", "            print(f'   {var[\"variable_name\"]}')\n", "        \n", "    except Exception as e:\n", "        print(f'❌ Error verifying Neo4j load: {e}')\n", "\n", "# Load to Neo4j\n", "load_to_neo4j(df_clean)\n", "\n", "print('\\n🎉 ONEINSIGHTS V11 ANALYSIS COMPLETE!')\n", "print('\\n✅ Achievements:')\n", "print('   🏗️ Complete hierarchy: PROJECT → APPLICATION → FOLDER → FILE → CLASS → METHOD → VARIABLE')\n", "print('   🔗 OneInsights project connects to BOTH applications')\n", "print('   📊 Variables standardized as ClassName.methodName.variableName')\n", "print('   🤖 LLM-enhanced semantic relationship extraction')\n", "print('   🔍 AST + LLM combined analysis for comprehensive coverage')\n", "print('   🧹 Clean, validated data loaded to Neo4j')\n", "print('   📁 Complete CSV export for analysis')\n", "print('\\n🎯 Ready for advanced graph analysis and lineage tracking!')\n", "print('\\n📋 Processing Summary:')\n", "print('   Stage 1: Setup and Configuration ✅')\n", "print('   Stage 2: File Discovery and Hierarchy Building ✅')\n", "print('   Stage 3: AST-based Code Analysis ✅')\n", "print('   Stage 4: Variable Standardization and Connection Building ✅')\n", "print('   Stage 4B: LLM Preparation with Current Context ✅')\n", "print('   Stage 4C: LLM Processing with Current Context ✅')\n", "print('   Stage 5: Data Cleaning and Validation ✅')\n", "print('   Stage 6: Neo4j Loading with Proper Structure ✅')"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}