{"cells": [{"cell_type": "code", "execution_count": 15, "id": "89043924", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! I'm doing great, thank you. How can I assist you today?\n"]}], "source": ["from langchain_openai import AzureChatOpenAI\n", "\n", "llm = AzureChatOpenAI(\n", "    api_key=\"CTWspeW2ExFB96wcMeB0Y43NP2qtgWBp9XTVO63ViMV5V5b6mFK8JQQJ99BGACYeBjFXJ3w3AAABACOG6YJ9\",\n", "    azure_endpoint=\"https://ms-data-lineage.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4.1-mini\",\n", "    api_version=\"2024-02-15-preview\"\n", ")\n", "\n", "response = llm.invoke(\"Hello, how are you?\")\n", "print(response.content)\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}