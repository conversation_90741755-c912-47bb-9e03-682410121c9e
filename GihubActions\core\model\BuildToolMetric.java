package com.bolt.dashboard.core.model;

import com.bolt.dashboard.core.model.BuildToolMetric;

/**
 * <AUTHOR>
 *
 */
public class BuildToolMetric {
    private String name;
    private Object valueBuildTool;
    private String formattedValueBuildTool;
    private String statusMessageBuildTool;

    public BuildToolMetric(String name) {
        this.name = name;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name
     *            the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return the value
     */
    public Object getValue() {
        return valueBuildTool;
    }

    /**
     * @param value
     *            the value to set
     */
    public void setValue(Object value) {
        this.valueBuildTool = value;
    }

    /**
     * @return the formattedValue
     */
    public String getFormattedValue() {
        return formattedValueBuildTool;
    }

    /**
     * @param formattedValue
     *            the formattedValue to set
     */
    public void setFormattedValue(String formattedValue) {
        this.formattedValueBuildTool = formattedValue;
    }

    /**
     * @return the statusMessage
     */
    public String getStatusMessage() {
        return statusMessageBuildTool;
    }

    /**
     * @param statusMessage
     *            the statusMessage to set
     */
    public void setStatusMessage(String statusMessage) {
        this.statusMessageBuildTool = statusMessage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        return name.equals(((BuildToolMetric) o).name);
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }

}