
```mermaid
flowchart TD
    A["Java Source Code"] --> B["Stage 1: Extract Folder + File Hierarchy"] & D["Stage 2: AST Extraction"]
    B --> G["Stage 3: LLM Processing"]
    D --> G
    G --> I["Language Based Chunks Splitting"]
    I --> J{"For Each Chunk"}
    J --> K["Build Enhanced System Prompt<br> using AST in Context"]
    K --> O{"More Chunks?"}
    O -- Yes --> J
    O -- No --> P["Stage 4: Data Processing and Normalization"]
    P --> S["Final Output CSV to Neo4j"]

     A:::normalProcess
     B:::normalProcess
     D:::normalProcess
     G:::normalProcess
     I:::llmHighlight
     J:::llmHighlight
     K:::llmHighlight
     O:::llmHighlight
     P:::normalProcess
     S:::normalProcess
    classDef llmHighlight fill:#ff9999,stroke:#ff0000,stroke-width:3px,color:#000
    classDef chunkHighlight fill:#ffcc99,stroke:#ff6600,stroke-width:2px,color:#000
    classDef normalProcess fill:#e1f5fe,stroke:#0277bd,stroke-width:1px,color:#000
    classDef csvOutput fill:#90EE90,stroke:#228B22,stroke-width:2px,color:#000



```

