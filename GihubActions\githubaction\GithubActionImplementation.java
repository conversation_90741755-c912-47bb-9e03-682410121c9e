package com.bolt.dashboard.githubaction;

import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;

import org.apache.commons.codec.binary.Base64;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;
import com.bolt.dashboard.core.model.BuildFailurePatternMetrics;
import com.bolt.dashboard.core.model.BuildSteps;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;

public class GithubActionImplementation implements GithubAction {

	private static final Logger LOGGER = LogManager.getLogger(GithubActionImplementation.class.getName());

	private static final String SEGMENT_API = "/api/v3/repos";
	private static final String PUBLIC_GITHUB_REPO_HOST = "api.github.com";
	private static final String PUBLIC_GITHUB_HOST_NAME = "github.move.com";
	AnnotationConfigApplicationContext ctx = null;
	String projectName = "";
	String userName = "";
	String password = "";
	private static long time = 0;
	BuildToolRep buildRepo = null;
	List<String> jobCollection = new ArrayList<>();
	String pipelineUrl = null;
	String singlePipelineUrl = null;
	String githubUrl = null;
	String jobsUrl = null;
	int size = 0;
	int lastPage = 0;
	long lastBuildId;
	private static long newbuildPipelineTimestamp;
	private static long timestamp;
	int page = 1;
	int per_page = 100;
	int totalPages;
	String pipelineId = null;

	BuildTool build;
	List<BuildSteps> jobsList;
	BuildFailurePatternForProjectRepo failurePatternRepo = null;
	String repoName;

	public void getBuildToolData(String baseUrl, BuildToolRep repo, boolean firstRun, String branch, String projectCode,
			String user, String pass, String projectName, String repoName) {

		String githubUrl = baseUrl;
		LOGGER.info("API  URL IS: {}", githubUrl);

		if (githubUrl.endsWith(".git")) {
			githubUrl = githubUrl.substring(0, githubUrl.lastIndexOf(".git"));
		}
		URL url = null;
		String hostName = "";
		String protocol = "";
		int port;
		try {
			url = new URL(githubUrl);
			hostName = url.getHost();
			protocol = url.getProtocol();
			port = url.getPort();
		} catch (MalformedURLException e) {
			LOGGER.error(e.getMessage());

		}
		String hostUrl = protocol + "://" + hostName + "/";
		if (repoName.equals("")) {
			String temp = githubUrl.substring(hostUrl.length(), githubUrl.length());
			repoName = temp.split("/")[1];

		}

		if (hostName.startsWith(PUBLIC_GITHUB_HOST_NAME)) {
			githubUrl = protocol + "://" + hostName + "/api/v3" + "/repos/"
					+ githubUrl.substring(hostUrl.length(), githubUrl.length());
		} else {
			githubUrl = protocol + "://" + PUBLIC_GITHUB_REPO_HOST + "/repos/"
					+ githubUrl.substring(hostUrl.length(), githubUrl.length());

			LOGGER.debug("API  URL: {}", githubUrl);
		}
		this.projectName = projectName;
		this.buildRepo = repo;
		this.repoName = repoName;
		page = 0;
		List<BuildTool> tool = repo.findByBuildTypeAndRepoNameAndNameOrderByTimestampDesc("GITHUB", repoName,
				projectName);
		String delta = "";
		if (tool != null && tool.size() > 0) {
			String timeString = "";
			int count = 0;
			while (timeString.equalsIgnoreCase("") && count < tool.size()) {
				timeString = tool.get(count).getTimeString();
				count++;
			}
			if (!timeString.equalsIgnoreCase("")) {
				delta = "&created=>" + timeString;
			}

		}
		ResponseEntity<String> response = makeRestCall(githubUrl + "/actions/runs?" + delta, user, pass);
		if (response != null) {
			LOGGER.info(githubUrl, "/actions/runs");
			JSONObject runs = new JSONObject(response.getBody());
			int totalRuns = runs.getInt("total_count");
			int page = 1;
			int runsCollected = 0;
			LOGGER.info("Builds to be Collected: " + totalRuns);

			while (runsCollected < totalRuns) {

				pipelineUrl = githubUrl + "/actions/runs?" + "page=" + page + "&per_page=" + per_page + delta;

				response = makeRestCall(pipelineUrl, user, pass);
				if (response != null) {
					runs = new JSONObject(response.getBody());
					JSONArray valueArray = runs.getJSONArray("workflow_runs");
					if (valueArray.length() > 0) {
						JSONObject obj = (JSONObject) valueArray.get(0);
						List<BuildTool> builds = processPipelineData(valueArray, user, pass, projectName, repoName,
								githubUrl);
						repo.save(builds);
						runsCollected += valueArray.length();
						page++;
						LOGGER.info("Builds Saved: " + runsCollected);
					} else
						break;

				}

			}

		}
	}

	private List<BuildTool> processPipelineData(JSONArray pipelineValues, String user, String pass, String projectName,
			String repoName, String githubUrl) {
		List<BuildTool> builds = new ArrayList<>();
		for (int i = 0; i < pipelineValues.length(); i++) {
			JSONObject pipeline_Obj = pipelineValues.getJSONObject(i);

			timestamp = getTimeInMiliseconds(pipeline_Obj.getString("created_at"));
			if (time < timestamp) {
				build = new BuildTool();
				long id = pipeline_Obj.getLong("id");

				build.setBuildID(id);
				build.setRepoName(repoName);
				this.pipelineId = "" + id;

				// pipeline jobs Details
				jobsUrl = githubUrl + "/actions/runs/" + this.pipelineId + "/jobs";

				ResponseEntity<String> jobResponse = makeRestCall(jobsUrl, user, pass);
				if (jobResponse != null) {
					if (build.getBuildID() <= lastBuildId)
						continue;

					build.setBranchName(pipeline_Obj.optString("head_branch"));
					build.setJobName(pipeline_Obj.optString("name"));
					build.setBuildType("GITHUB");
					build.setName(projectName);
					try {
						build.setTimeString(pipeline_Obj.getString("created_at"));
					} catch (Exception e) {

					}
					String temp_date = null;
					String temp_date1 = null;
					if (!pipeline_Obj.isNull("created_at")) {
						temp_date = pipeline_Obj.optString("created_at");
					}
					long createTime = 0;
					if (temp_date != null) {
						createTime = getTimeInMiliseconds(temp_date);
					}

					if (!pipeline_Obj.isNull("updated_at")) {
						temp_date1 = pipeline_Obj.optString("updated_at");
					}
					long updatedTime = 0;
					if (temp_date1 != null) {
						updatedTime = getTimeInMiliseconds(temp_date1);
					}
					build.setTimestamp(createTime);
					temp_date = null;
					if (!pipeline_Obj.isNull("created_at")) {
						temp_date = pipeline_Obj.getString("created_at");
					}
					long completeTime = 0;
					if (temp_date != null) {
						completeTime = getTimeInMiliseconds(temp_date);
					}
					// Building the Metrics Data of Result, duration, timestamp

					BuildToolMetric durationMetric = new BuildToolMetric("duration");
					long duration = 0;

					duration = updatedTime - createTime;
					durationMetric.setValue(duration);
					BuildToolMetric resultMetric = new BuildToolMetric("result");
					resultMetric.setValue(pipeline_Obj.optString("conclusion"));
					BuildToolMetric timestampMetric = new BuildToolMetric("timestamp");
					timestampMetric.setValue(completeTime);

					build.getMetrics().add(resultMetric);
					build.getMetrics().add(durationMetric);
					build.getMetrics().add(timestampMetric);
					JSONObject jobsArray1 = new JSONObject(jobResponse.getBody());
					JSONArray jobsArray = jobsArray1.getJSONArray("jobs");
					if (jobsArray.length() > 0) {
						JSONObject singleJobsobj = jobsArray.getJSONObject(0);
						JSONObject commitObj = pipeline_Obj.getJSONObject("head_commit");
						JSONObject authorObj = commitObj.getJSONObject("author");
						build.setCreatedBy(authorObj.optString("name"));
						build.setTriggerType(authorObj.optString("name"));
						jobsList = new ArrayList<>();
						for (int j = 0; j < jobsArray.length(); j++) {
							BuildSteps tempSteps = new BuildSteps();
							try {
								JSONObject stepsObj = jobsArray.getJSONObject(j);
								String completeDate = null;
								long completed = 0;
								long started = 0;
								if (!stepsObj.isNull("completed_at")) {
									completeDate = stepsObj.getString("completed_at");
								}
								long tempDate = 0;

								if (completeDate != null) {
									tempDate = getTimeInMiliseconds(completeDate);
									completed = tempDate;
								}
								tempSteps.setCompletedTime(tempDate);
								completeDate = null;
								if (!stepsObj.isNull("started_at")) {
									completeDate = stepsObj.getString("started_at");
								}
								if (completeDate != null) {
									tempDate = getTimeInMiliseconds(completeDate);
									started = tempDate;
								}
								tempSteps.setStartedTime(tempDate);
								tempSteps.setDuration(completed - started);
								tempSteps.setStepName(stepsObj.optString("name"));
								String result = stepsObj.optString("conclusion");
								if (result.toLowerCase().equals("failure")) {
									processFailure(githubUrl, user, pass, stepsObj.getLong("id"));
								}

								tempSteps.setResult(result);
							} catch (JSONException e) {

								LOGGER.error("parsing esception", e.getCause());

							}
							jobsList.add(tempSteps);
						}
					}
					build.setStepsList(jobsList);

					builds.add(build);
				}

			}
		}
		return builds;
	}

	private void processFailure(String githubUrl, String user, String pass, long id) {

		ctx = DataConfig.getContext();
		failurePatternRepo = ctx.getBean(BuildFailurePatternForProjectRepo.class);
		List<BuildFailurePatternForProjectInJenkinsModel> failurePattern = failurePatternRepo
				.findByProjectName(projectName);
		try {
			if (!failurePattern.isEmpty()) {
				BuildFailurePatternForProjectInJenkinsModel tempBuildFailure = failurePattern.get(0);
				List<BuildFailurePatternMetrics> failureMetrics = tempBuildFailure.getPatternMetrics();
				List<BuildFailurePatternMetrics> newList = new ArrayList<>();
				String url = githubUrl + "/actions/jobs/" + id + "/logs";
				ResponseEntity<String> logResponseData = makeRestCall(url, user, pass);
				if (logResponseData != null) {
					String failureLog = logResponseData.getBody();
					for (BuildFailurePatternMetrics temp : failureMetrics) {
						if (null != temp.getRepoName() && temp.getRepoName().equals(repoName)) {
							if (failureLog.contains(temp.getPatternDefined())) {
								temp.setPatternCount(temp.getPatternCount() + 1);
							}
						} else {
							boolean flag = true;

							for (BuildFailurePatternMetrics n : failureMetrics) {
								if (n.getPatternDefined().equals(temp.getPatternDefined()) && null != n.getRepoName()
										&& n.getRepoName().equals(repoName)) {
									if (failureLog.contains(n.getPatternDefined())) {
										n.setPatternCount(n.getPatternCount() + 1);
									}
									flag = false;
								}
							}

							if (flag) {
								BuildFailurePatternMetrics b = new BuildFailurePatternMetrics();
								b.setPatternCount(0);
								b.setPatternDefined(temp.getPatternDefined());
								b.setPatternDisplayed(temp.getPatternDisplayed());
								b.setRepoName(repoName);
								if (failureLog.contains(b.getPatternDefined())) {
									b.setPatternCount(b.getPatternCount() + 1);
								}
								newList.add(b);
							}

						}
					}

					failureMetrics.addAll(newList);
					// ResponseEntity<String> logResponseData = makeRestCall(url);
					// String failureLog = logResponseData.getBody();
					// for (BuildFailurePatternMetrics temp : failureMetrics) {
					// if (failureLog.contains(temp.getPatternDefined())) {
					// temp.setPatternCount(temp.getPatternCount() + 1);
					// }
					// }
					failurePatternRepo.save(tempBuildFailure);
				}
			}
		} catch (Exception e) {
			LOGGER.error("ProcessFailure Exception/ Empty in calling Gitlab Pipeline ", e.fillInStackTrace());

		}
	}

	private long getTimeInMiliseconds(String temp_date) {

		String[] splitDate = temp_date.split("T");
		String[] temp = splitDate[1].split("\\.");
		temp = temp[0].split("Z");
		String tempTime = splitDate[0] + " " + temp[0];

		DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
		DateTime createdDate = fmt.parseDateTime(tempTime);
		return createdDate.getMillis();
	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password) {
		try {
			// Basic Auth only.
			if (!"".equals(userId) && !"".equals(password)) {
				return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)),
						String.class);
			} else {
				return get().exchange(url, HttpMethod.GET, null, String.class);
			}
		} catch (Exception ex) {
			LOGGER.info(ex);
			ex.printStackTrace();
			return null;

		}
	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

}
