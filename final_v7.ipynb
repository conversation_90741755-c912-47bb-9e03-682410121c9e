{"cells": [{"cell_type": "code", "execution_count": 13, "id": "0d635234", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Shaik\\sample\\venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4808\\4130998996.py:24: LangChainDeprecationWarning: The class `Neo4jGraph` was deprecated in LangChain 0.3.8 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-neo4j package and should be used instead. To use it run `pip install -U :class:`~langchain-neo4j` and import as `from :class:`~langchain_neo4j import Neo4jGraph``.\n", "  graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n"]}], "source": ["import os\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "import re\n", "from collections import defaultdict\n", "\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "BASE_PATH = Path(r\"C:/Shaik/sample/OneInsights\")\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"oneinsights\"\n", "GOOGLE_API_KEY = \"AIzaSyAeSuntl3dxGqQhwxRG_jom1V_EjxEPSwc\"\n", "\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "llm = ChatGoogleGenerativeAI(\n", "    model=\"gemini-2.5-pro\",\n", "    temperature=0,\n", "    google_api_key=GOOGLE_API_KEY\n", ")"]}, {"cell_type": "code", "execution_count": 3, "id": "81bc72fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 1 Complete: 8 folder relationships, 19 file relationships\n"]}], "source": ["\n", "# ========== STAGE 1: FOLDER + FILE HIERARCHY ==========\n", "def extract_folder_file_hierarchy(base_path):\n", "    folder_records, file_records = [], []\n", "    base_path = os.path.abspath(base_path)\n", "    base_folder_name = os.path.basename(base_path)\n", "    processed_folders = set()\n", "\n", "    for root, dirs, files in os.walk(base_path):\n", "        rel_root = os.path.relpath(root, base_path)\n", "        parent_folder = base_folder_name if rel_root == '.' else os.path.dirname(rel_root) or base_folder_name\n", "        current_folder = base_folder_name if rel_root == '.' else os.path.basename(rel_root)\n", "\n", "        folder_key = f'{parent_folder}->{current_folder}'\n", "        if folder_key not in processed_folders and parent_folder != current_folder:\n", "            folder_records.append({\n", "                'source_node': parent_folder,\n", "                'source_type': 'folder',\n", "                'destination_node': current_folder,\n", "                'destination_type': 'folder',\n", "                'relationship': 'contains',\n", "                'file_path': None\n", "            })\n", "            processed_folders.add(folder_key)\n", "\n", "        for f in files:\n", "            if f.endswith('.java'):\n", "                file_rel_path = os.path.relpath(os.path.join(root, f), base_path)\n", "                file_records.append({\n", "                    'source_node': current_folder,\n", "                    'source_type': 'folder',\n", "                    'destination_node': f,\n", "                    'destination_type': 'file',\n", "                    'relationship': 'contains',\n", "                    'file_path': file_rel_path\n", "                })\n", "    return folder_records, file_records\n", "\n", "folder_records, file_records = extract_folder_file_hierarchy(BASE_PATH)\n", "df_folders = pd.DataFrame(folder_records)\n", "df_files = pd.DataFrame(file_records)\n", "\n", "print(f'Stage 1 Complete: {len(df_folders)} folder relationships, {len(df_files)} file relationships')"]}, {"cell_type": "code", "execution_count": 4, "id": "a3cb1a9b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>OneInsights</td>\n", "      <td>folder</td>\n", "      <td>ServiceBolt</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ServiceBolt</td>\n", "      <td>folder</td>\n", "      <td>api</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ServiceBolt</td>\n", "      <td>folder</td>\n", "      <td>service</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>OneInsights</td>\n", "      <td>folder</td>\n", "      <td>UnifiedBolt</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>UnifiedBolt</td>\n", "      <td>folder</td>\n", "      <td>core</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>UnifiedBolt\\core</td>\n", "      <td>folder</td>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>UnifiedBolt\\core</td>\n", "      <td>folder</td>\n", "      <td>repository</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>UnifiedBolt</td>\n", "      <td>folder</td>\n", "      <td>githubaction</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        source_node source_type destination_node destination_type  \\\n", "0       OneInsights      folder      ServiceBolt           folder   \n", "1       ServiceBolt      folder              api           folder   \n", "2       ServiceBolt      folder          service           folder   \n", "3       OneInsights      folder      UnifiedBolt           folder   \n", "4       UnifiedBolt      folder             core           folder   \n", "5  UnifiedBolt\\core      folder            model           folder   \n", "6  UnifiedBolt\\core      folder       repository           folder   \n", "7       UnifiedBolt      folder     githubaction           folder   \n", "\n", "  relationship file_path  \n", "0     contains      None  \n", "1     contains      None  \n", "2     contains      None  \n", "3     contains      None  \n", "4     contains      None  \n", "5     contains      None  \n", "6     contains      None  \n", "7     contains      None  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_folders"]}, {"cell_type": "code", "execution_count": 5, "id": "77f04b3f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>api</td>\n", "      <td>folder</td>\n", "      <td>BuildToolController.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>ServiceBolt\\api\\BuildToolController.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>service</td>\n", "      <td>folder</td>\n", "      <td>BuildToolService.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>ServiceBolt\\service\\BuildToolService.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>service</td>\n", "      <td>folder</td>\n", "      <td>BuildToolServiceImplemantation.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>ServiceBolt\\service\\BuildToolServiceImplemanta...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>BaseModel.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\BaseModel.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>BuildFailurePatternForProjectInJenkinsModel.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildFailurePatternForP...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  source_node source_type                                  destination_node  \\\n", "0         api      folder                          BuildToolController.java   \n", "1     service      folder                             BuildToolService.java   \n", "2     service      folder               BuildToolServiceImplemantation.java   \n", "3       model      folder                                    BaseModel.java   \n", "4       model      folder  BuildFailurePatternForProjectInJenkinsModel.java   \n", "\n", "  destination_type relationship  \\\n", "0             file     contains   \n", "1             file     contains   \n", "2             file     contains   \n", "3             file     contains   \n", "4             file     contains   \n", "\n", "                                           file_path  \n", "0           ServiceBolt\\api\\BuildToolController.java  \n", "1          ServiceBolt\\service\\BuildToolService.java  \n", "2  ServiceBolt\\service\\BuildToolServiceImplemanta...  \n", "3              UnifiedBolt\\core\\model\\BaseModel.java  \n", "4  UnifiedBolt\\core\\model\\BuildFailurePatternForP...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_files.head(5)"]}, {"cell_type": "code", "execution_count": 6, "id": "b09fb3b0", "metadata": {}, "outputs": [], "source": ["\n", "# ========== UTILITY FUNCTIONS ==========\n", "def read_source_code(file_path):\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        return f.read().encode('utf-8')\n", "\n", "def extract_package_and_imports(source_code_str):\n", "    package_pattern = r'package\\s+([\\w\\.]+);'\n", "    import_pattern = r'import\\s+([\\w\\.]+);'\n", "    package_match = re.search(package_pattern, source_code_str)\n", "    package_name = package_match.group(1) if package_match else None\n", "    import_matches = re.findall(import_pattern, source_code_str)\n", "    return package_name, import_matches\n", "\n", "def extract_api_endpoints(source_code_str):\n", "    endpoints = []\n", "    # Enhanced patterns to catch more API endpoint variations\n", "    mapping_patterns = {\n", "        'RequestMapping': [\n", "            r'@RequestMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@RequestMapping\\s*\\(\\s*path\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'GetMapping': [\n", "            r'@GetMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@GetMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@GetMapping\\s*\\(\\s*path\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'PostMapping': [\n", "            r'@PostMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@PostMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@PostMapping\\s*\\(\\s*path\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'PutMapping': [\n", "            r'@PutMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@PutMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'DeleteMapping': [\n", "            r'@DeleteMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@DeleteMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'PatchMapping': [\n", "            r'@PatchMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@PatchMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ]\n", "    }\n", "    \n", "    for mapping_type, patterns in mapping_patterns.items():\n", "        for pattern in patterns:\n", "            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)\n", "            for match in matches:\n", "                if match.strip():  # Only add non-empty matches\n", "                    endpoints.append({\n", "                        'type': mapping_type,\n", "                        'path': match.strip(),\n", "                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'\n", "                    })\n", "    \n", "    # Also look for @RestController and @Controller classes\n", "    controller_pattern = r'@(RestController|Controller)'\n", "    if re.search(controller_pattern, source_code_str):\n", "        # Extract class-level RequestMapping\n", "        class_mapping = re.search(r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "        base_path = class_mapping.group(1) if class_mapping else \"\"\n", "        \n", "        # Find methods with mappings\n", "        method_pattern = r'@(GetMapping|PostMapping|PutMapping|DeleteMapping|PatchMapping|RequestMapping)\\s*(?:\\([^)]*\\))?\\s*\\n\\s*(?:public|private|protected)?\\s*\\w+\\s+(\\w+)\\s*\\('\n", "        method_matches = re.findall(method_pattern, source_code_str, re.MULTILINE)\n", "        \n", "        for mapping_type, method_name in method_matches:\n", "            if base_path:\n", "                full_path = f\"{base_path.rstrip('/')}/{method_name}\"\n", "            else:\n", "                full_path = f\"/{method_name}\"\n", "            \n", "            endpoints.append({\n", "                'type': mapping_type,\n", "                'path': full_path,\n", "                'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET',\n", "                'method_name': method_name\n", "            })\n", "    \n", "    return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "    entities = []\n", "    \n", "    # Enhanced Entity detection\n", "    entity_patterns = [\n", "        r'@Entity\\s*(?:\\([^)]*\\))?',\n", "        r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "        r'@Entity\\s*\\n\\s*@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "    ]\n", "    \n", "    for pattern in entity_patterns:\n", "        if re.search(pattern, source_code_str, re.MULTILINE):\n", "            # Extract table name from @Table annotation\n", "            table_matches = re.findall(r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "            for table_name in table_matches:\n", "                if table_name.strip():\n", "                    entities.append({\n", "                        'type': 'table',\n", "                        'name': table_name.strip()\n", "                    })\n", "            \n", "            # If no @Table, try to infer from class name\n", "            if not table_matches:\n", "                class_match = re.search(r'public\\s+class\\s+(\\w+)', source_code_str)\n", "                if class_match:\n", "                    class_name = class_match.group(1)\n", "                    # Convert CamelCase to snake_case for table name\n", "                    table_name = re.sub('([a-z0-9])([A-Z])', r'\\1_\\2', class_name).lower()\n", "                    entities.append({\n", "                        'type': 'table',\n", "                        'name': table_name\n", "                    })\n", "    \n", "    # Enhanced Query detection\n", "    query_patterns = [\n", "        r'@Query\\s*\\(\\s*[\"\\']([^\"\\']*(?:FROM|from)\\s+([\\w]+)[^\"\\']*)[\"\\']',\n", "        r'@Query\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']*(?:FROM|from)\\s+([\\w]+)[^\"\\']*)[\"\\']',\n", "        r'@Query\\s*\\(\\s*nativeQuery\\s*=\\s*true\\s*,\\s*value\\s*=\\s*[\"\\']([^\"\\']*(?:FROM|from)\\s+([\\w]+)[^\"\\']*)[\"\\']'\n", "    ]\n", "    \n", "    for pattern in query_patterns:\n", "        query_matches = re.findall(pattern, source_code_str, re.MULTILINE | re.IGNORECASE)\n", "        for match in query_matches:\n", "            if isinstance(match, tuple) and len(match) >= 2:\n", "                table_name = match[1].strip()\n", "                if table_name and table_name.lower() not in ['select', 'where', 'order', 'group']:\n", "                    entities.append({\n", "                        'type': 'table',\n", "                        'name': table_name\n", "                    })\n", "    \n", "    # Repository pattern detection\n", "    repository_pattern = r'interface\\s+(\\w+)\\s+extends\\s+.*Repository'\n", "    repo_matches = re.findall(repository_pattern, source_code_str)\n", "    for repo_name in repo_matches:\n", "        # Extract entity name from repository name (e.g., UserRepository -> User)\n", "        entity_name = repo_name.replace('Repository', '')\n", "        if entity_name:\n", "            entities.append({\n", "                'type': 'table',\n", "                'name': entity_name.lower()\n", "            })\n", "    \n", "    return entities"]}, {"cell_type": "code", "execution_count": 7, "id": "f5045662", "metadata": {}, "outputs": [], "source": ["\n", "\n", "def extract_interface_extends(source_code_str):\n", "    extends_relationships = []\n", "    \n", "    # Interface extends\n", "    interface_extends_pattern = r'interface\\s+(\\w+)\\s+extends\\s+([\\w<>,\\s]+)'\n", "    matches = re.findall(interface_extends_pattern, source_code_str)\n", "    for interface_name, extends_clause in matches:\n", "        parent_interfaces = [part.strip().split('<')[0].strip() for part in extends_clause.split(',')]\n", "        for parent in parent_interfaces:\n", "            if parent:\n", "                extends_relationships.append({\n", "                    'child_interface': interface_name,\n", "                    'parent_interface': parent,\n", "                    'full_extends': extends_clause.strip(),\n", "                    'type': 'interface_extends'\n", "                })\n", "    \n", "    # Class extends\n", "    class_extends_pattern = r'class\\s+(\\w+)\\s+extends\\s+([\\w<>]+)'\n", "    class_matches = re.findall(class_extends_pattern, source_code_str)\n", "    for child_class, parent_class in class_matches:\n", "        # Clean generic types\n", "        parent_class = re.sub(r'<.*?>', '', parent_class).strip()\n", "        if parent_class:\n", "            extends_relationships.append({\n", "                'child_interface': child_class,\n", "                'parent_interface': parent_class,\n", "                'full_extends': parent_class,\n", "                'type': 'class_extends'\n", "            })\n", "    \n", "    # Class implements\n", "    implements_pattern = r'class\\s+(\\w+)(?:\\s+extends\\s+\\w+)?\\s+implements\\s+([\\w<>,\\s]+)'\n", "    impl_matches = re.findall(implements_pattern, source_code_str)\n", "    for class_name, implements_clause in impl_matches:\n", "        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]\n", "        for interface in interfaces:\n", "            if interface:\n", "                extends_relationships.append({\n", "                    'child_interface': class_name,\n", "                    'parent_interface': interface,\n", "                    'full_extends': implements_clause.strip(),\n", "                    'type': 'class_implements'\n", "                })\n", "    \n", "    return extends_relationships"]}, {"cell_type": "code", "execution_count": 8, "id": "aec994ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Found 11 endpoints in BuildToolController\n", " Found 1 DB entities in BuildFailurePatternForProjectRepo\n", " Found 1 DB entities in BuildToolRep\n", " Found 1 DB entities in ConfigurationSettingRep\n", " Class registry built with 19 classes\n"]}], "source": ["\n", "\n", "def build_enhanced_class_registry():\n", "    class_registry = {}\n", "    for root, _, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.endswith('.java'):\n", "                file_path = os.path.join(root, file)\n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        source_code_str = f.read()\n", "                    \n", "                    package_name, imports = extract_package_and_imports(source_code_str)\n", "                    endpoints = extract_api_endpoints(source_code_str)\n", "                    db_entities = extract_database_entities(source_code_str)\n", "                    interface_extends = extract_interface_extends(source_code_str)\n", "                    \n", "                    class_name = file.replace('.java', '')\n", "                    fqcn = f'{package_name}.{class_name}' if package_name else class_name\n", "                    \n", "                    class_registry[class_name] = {\n", "                        'fqcn': fqcn,\n", "                        'package': package_name,\n", "                        'file_path': file_path,\n", "                        'imports': imports,\n", "                        'endpoints': endpoints,\n", "                        'db_entities': db_entities,\n", "                        'interface_extends': interface_extends\n", "                    }\n", "                    \n", "                    # Debug output for endpoints and DB entities\n", "                    if endpoints:\n", "                        print(f\" Found {len(endpoints)} endpoints in {class_name}\")\n", "                    if db_entities:\n", "                        print(f\" Found {len(db_entities)} DB entities in {class_name}\")\n", "                        \n", "                except Exception as e:\n", "                    print(f\" Error processing {file}: {e}\")\n", "                    continue\n", "    \n", "    return class_registry\n", "\n", "class_registry = build_enhanced_class_registry()\n", "print(f' Class registry built with {len(class_registry)} classes')"]}, {"cell_type": "code", "execution_count": 9, "id": "58fb060c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 2 Complete: 715 AST relationships extracted\n"]}], "source": ["# ========== STAGE 2: AST EXTRACTION ==========\n", "\n", "def extract_ast_structure(file_path):\n", "    records = []\n", "    source_code = read_source_code(file_path)\n", "    tree = parser.parse(source_code)\n", "    root_node = tree.root_node\n", "    file_name = os.path.basename(file_path)\n", "\n", "    def clean_node_name(name):\n", "        \"\"\"Clean node names to remove prefixes and suffixes\"\"\"\n", "        if not name:\n", "            return name\n", "        \n", "        # Remove common prefixes\n", "        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']\n", "        for prefix in prefixes_to_remove:\n", "            if name.lower().startswith(prefix):\n", "                name = name[len(prefix):]\n", "        \n", "        # Remove file extensions\n", "        name = re.sub(r'\\.(java|class)$', '', name, flags=re.IGNORECASE)\n", "        \n", "        return name.strip()\n", "\n", "    def traverse(node, parent_type=None, parent_name=None):\n", "        # Handle class declarations\n", "        if node.type == 'class_declaration':\n", "            class_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    # File -> Class relationship\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'file',\n", "                        'destination_node': class_name,\n", "                        'destination_type': 'class',\n", "                        'relationship': 'declares',\n", "                        'file_path': file_path\n", "                    })\n", "                    \n", "                    # Add API endpoints from registry\n", "                    class_info = class_registry.get(class_name, {})\n", "                    endpoints = class_info.get('endpoints', [])\n", "                    for ep in endpoints:\n", "                        endpoint_name = f\"{ep['method']} {ep['path']}\"\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': endpoint_name,\n", "                            'destination_type': 'endpoint',\n", "                            'relationship': 'declares',\n", "                            'file_path': file_path\n", "                        })\n", "                    \n", "                    # Add database entities from registry\n", "                    db_entities = class_info.get('db_entities', [])\n", "                    for entity in db_entities:\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': entity['name'],\n", "                            'destination_type': 'table',\n", "                            'relationship': 'maps_to',\n", "                            'file_path': file_path\n", "                        })\n", "                    \n", "                    # Add extends/implements relationships from registry\n", "                    interface_extends = class_info.get('interface_extends', [])\n", "                    for ext_rel in interface_extends:\n", "                        rel_type = 'extends' if ext_rel.get('type') == 'class_extends' else 'implements'\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': ext_rel['parent_interface'],\n", "                            'destination_type': 'interface' if rel_type == 'implements' else 'class',\n", "                            'relationship': rel_type,\n", "                            'file_path': file_path\n", "                        })\n", "                    break\n", "            \n", "            # Traverse children with class context\n", "            for child in node.children:\n", "                traverse(child, 'class', class_name)\n", "                \n", "        # Handle interface declarations\n", "        elif node.type == 'interface_declaration':\n", "            interface_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    interface_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'file',\n", "                        'destination_node': interface_name,\n", "                        'destination_type': 'interface',\n", "                        'relationship': 'declares',\n", "                        'file_path': file_path\n", "                    })\n", "                    break\n", "            \n", "            # Traverse children with interface context\n", "            for child in node.children:\n", "                traverse(child, 'interface', interface_name)\n", "                \n", "        # Handle method declarations - FIXED HIERARCHY\n", "        elif node.type == 'method_declaration':\n", "            method_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    # CORRECT: Class -> Method (not Method -> Class)\n", "                    if parent_name and parent_type in ['class', 'interface']:\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': method_name,\n", "                            'destination_type': 'method',\n", "                            'relationship': 'declares',\n", "                            'file_path': file_path\n", "                        })\n", "                    break\n", "            \n", "            # Traverse children with method context\n", "            for child in node.children:\n", "                traverse(child, 'method', method_name)\n", "                \n", "        # Handle field declarations - FIXED HIERARCHY\n", "        elif node.type == 'field_declaration':\n", "            for child in node.children:\n", "                if child.type == 'variable_declarator':\n", "                    for grandchild in child.children:\n", "                        if grandchild.type == 'identifier':\n", "                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))\n", "                            \n", "                            # CORRECT: Class -> Variable (not Variable -> Class)\n", "                            if parent_name and parent_type == 'class':\n", "                                records.append({\n", "                                    'source_node': parent_name,\n", "                                    'source_type': parent_type,\n", "                                    'destination_node': field_name,\n", "                                    'destination_type': 'variable',\n", "                                    'relationship': 'has_field',\n", "                                    'file_path': file_path\n", "                                })\n", "                                \n", "        # Handle variable usage in methods - FIXED HIERARCHY\n", "        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if var_name and var_name != 'this' and parent_name:\n", "                        # CORRECT: Method -> Variable (not Variable -> Method)\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': var_name,\n", "                            'destination_type': 'variable',\n", "                            'relationship': 'uses',\n", "                            'file_path': file_path\n", "                        })\n", "                        \n", "        # Handle field access in methods\n", "        elif node.type == 'field_access' and parent_type == 'method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    field_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if field_name and field_name != 'this' and parent_name:\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': field_name,\n", "                            'destination_type': 'variable',\n", "                            'relationship': 'uses',\n", "                            'file_path': file_path\n", "                        })\n", "        \n", "        # Handle return statements\n", "        elif node.type == 'return_statement' and parent_type == 'method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if var_name and parent_name:\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': var_name,\n", "                            'destination_type': 'variable',\n", "                            'relationship': 'uses',\n", "                            'file_path': file_path\n", "                        })\n", "        else:\n", "            # Continue traversing for other node types\n", "            for child in node.children:\n", "                traverse(child, parent_type, parent_name)\n", "\n", "    traverse(root_node)\n", "    return records\n", "\n", "ast_records = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                ast_records.extend(extract_ast_structure(file_path))\n", "            except Exception as e:\n", "                print(f' Error processing {file}: {e}')\n", "                continue\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "print(f'Stage 2 Complete: {len(df_ast)} AST relationships extracted')"]}, {"cell_type": "code", "execution_count": 10, "id": "9df9dd28", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>ConfigurationSetting.java</td>\n", "      <td>file</td>\n", "      <td>ConfigurationSetting</td>\n", "      <td>class</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>232</th>\n", "      <td>BuildTool</td>\n", "      <td>class</td>\n", "      <td>timeString</td>\n", "      <td>variable</td>\n", "      <td>has_field</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>459</th>\n", "      <td>ConfigurationToolInfoMetric</td>\n", "      <td>class</td>\n", "      <td>setDomain</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>search</td>\n", "      <td>method</td>\n", "      <td>mongoAggr</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\servic...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>BuildInfo</td>\n", "      <td>class</td>\n", "      <td>committer</td>\n", "      <td>variable</td>\n", "      <td>has_field</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     source_node source_type      destination_node  \\\n", "363    ConfigurationSetting.java        file  ConfigurationSetting   \n", "232                    BuildTool       class            timeString   \n", "459  ConfigurationToolInfoMetric       class             setDomain   \n", "87                        search      method             mongoAggr   \n", "187                    BuildInfo       class             committer   \n", "\n", "    destination_type relationship  \\\n", "363            class     declares   \n", "232         variable    has_field   \n", "459           method     declares   \n", "87          variable         uses   \n", "187         variable    has_field   \n", "\n", "                                             file_path  \n", "363  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...  \n", "232  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...  \n", "459  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...  \n", "87   C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\servic...  \n", "187  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df_ast.sample(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5cfb4bd3", "metadata": {}, "outputs": [], "source": ["\n", "splitter = RecursiveCharacterTextSplitter.from_language(\n", "    language=LC_Language.JAVA,\n", "    chunk_size=4000,\n", "    chunk_overlap=200\n", ")\n", "\n", "java_docs, split_docs = [], []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            try:\n", "                loader = TextLoader(os.path.join(root, file))\n", "                java_docs.extend(loader.load())\n", "            except Exception as e:\n", "                print(f\" Error loading {file}: {e}\")\n", "                continue\n", "\n", "for doc in java_docs:\n", "    split_docs.extend(splitter.split_documents([doc]))"]}, {"cell_type": "code", "execution_count": 11, "id": "3d076121", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 3: LLM EXTRACTION WITH AST CONTEXT ==========\n", "\n", "\n", "def build_enhanced_system_prompt(file_path, ast_df, class_registry):\n", "    ast_subset = ast_df[ast_df['file_path'] == file_path] if len(ast_df) > 0 else pd.DataFrame()\n", "    ast_context = ''\n", "    for _, row in ast_subset.iterrows():\n", "        ast_context += f\"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']}\\n\"\n", "    \n", "    registry_context = 'Known Classes:\\n'\n", "    for class_name, info in class_registry.items():\n", "        registry_context += f'- {class_name} (FQCN: {info[\"fqcn\"]})\\n'\n", "        if len(info.get('endpoints', [])) > 0:\n", "            registry_context += f'  * {len(info[\"endpoints\"])} API endpoint(s)\\n'\n", "        if len(info.get('db_entities', [])) > 0:\n", "            registry_context += f'  * {len(info[\"db_entities\"])} DB entity/entities\\n'\n", "    \n", "    prompt = f\"\"\"\n", "You are a Java code lineage extraction engine. Extract relationships between code entities with STRICT focus on:\n", "\n", "CONTEXT:\n", "{registry_context}\n", "\n", "AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):\n", "{ast_context}\n", "\n", "CRITICAL RULES - <PERSON><PERSON><PERSON><PERSON> EXACTLY:\n", "1. Use SIMPLE names only (remove prefixes like \"method:\", \"class:\", etc.)\n", "2. MANDATORY RELATIONSHIP DIRECTIONS (DO NOT REVERSE):\n", "   - file -[declares]-> class\n", "   - class -[declares]-> method  \n", "   - class -[has_field]-> variable\n", "   - method -[uses]-> variable\n", "   - class -[declares]-> endpoint\n", "   - class -[maps_to]-> table\n", "3. Extract REST API endpoints as 'endpoint' nodes (GET /api/users, POST /api/data)\n", "4. Extract database tables from @Entity, @Table, @Query annotations\n", "5. Extract interface extends and class implements relationships\n", "6. NEVER create reverse relationships (method->class, variable->method, etc.)\n", "7. Follow the AST RELATIONSHIPS above for correct structure\n", "8. Clean node names (remove \"method:\", \"class:\" prefixes)\n", "\n", "Extract triples in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the triples, no explanations.\n", "\"\"\"\n", "    return prompt"]}, {"cell_type": "code", "execution_count": null, "id": "35cf8070", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Stage 3: Enhanced LLM Extraction: 100%|██████████| 32/32 [27:09<00:00, 50.91s/it]"]}, {"name": "stdout", "output_type": "stream", "text": [" Stage 3 Complete: 1250 LLM relationships extracted\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["\n", "for chunk in tqdm(split_docs, desc='Stage 3: Enhanced LLM Extraction'):\n", "    file_path = chunk.metadata.get('source')\n", "    system_prompt = build_enhanced_system_prompt(file_path, df_ast, class_registry)\n", "    \n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=system_prompt,\n", "        allowed_nodes=['file', 'class', 'interface', 'method', 'variable', 'table', 'endpoint'],\n", "        allowed_relationships=[\n", "            ('file', 'declares', 'class'),\n", "            ('file', 'declares', 'interface'),\n", "            ('class', 'declares', 'method'),\n", "            ('interface', 'declares', 'method'),\n", "            ('class', 'declares', 'endpoint'),\n", "            ('method', 'calls', 'method'),\n", "            ('class', 'has_field', 'variable'),\n", "            ('method', 'uses', 'variable'),\n", "            ('class', 'uses', 'class'),\n", "            ('interface', 'extends', 'interface'),\n", "            ('class', 'extends', 'class'),\n", "            ('class', 'implements', 'interface'),\n", "            ('class', 'maps_to', 'table'),\n", "            ('method', 'reads_from', 'table'),\n", "            ('method', 'writes_to', 'table'),\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False,\n", "    )\n", "    \n", "    try:\n", "        graph_docs = transformer.convert_to_graph_documents([chunk])\n", "        for gd in graph_docs:\n", "            for rel in gd.relationships:\n", "                s_node = rel.source.id.strip()\n", "                s_type = rel.source.type.strip().lower()\n", "                t_node = rel.target.id.strip()\n", "                t_type = rel.target.type.strip().lower()\n", "                rel_type = rel.type.strip().lower()\n", "\n", "                def normalize_entity(entity_name, entity_type):\n", "                    if not entity_name:\n", "                        return entity_name\n", "                    \n", "                    # Remove prefixes\n", "                    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "                    for prefix in prefixes:\n", "                        if entity_name.lower().startswith(prefix):\n", "                            entity_name = entity_name[len(prefix):]\n", "                    \n", "                    # Remove file extensions\n", "                    entity_name = re.sub(r'\\.(java|class)$', '', entity_name, flags=re.IGNORECASE)\n", "                    \n", "                    # Clean dots for class names\n", "                    if entity_type in ['class', 'method'] and '.' in entity_name:\n", "                        entity_name = entity_name.split('.')[-1]\n", "                    \n", "                    # Match with class registry for consistency\n", "                    if entity_type == 'class':\n", "                        for class_name in class_registry.keys():\n", "                            if entity_name.lower() == class_name.lower():\n", "                                return class_name.lower()\n", "                    \n", "                    return entity_name.lower()\n", "\n", "                s_node = normalize_entity(s_node, s_type)\n", "                t_node = normalize_entity(t_node, t_type)\n", "\n", "                # Skip invalid relationships\n", "                if s_node == t_node and s_type == t_type:\n", "                    continue\n", "                if not s_node or not t_node:\n", "                    continue\n", "                \n", "                # Enforce correct relationship directions\n", "                valid_directions = {\n", "                    ('file', 'declares', 'class'),\n", "                    ('file', 'declares', 'interface'),\n", "                    ('class', 'declares', 'method'),\n", "                    ('interface', 'declares', 'method'),\n", "                    ('class', 'declares', 'endpoint'),\n", "                    ('class', 'has_field', 'variable'),\n", "                    ('method', 'uses', 'variable'),\n", "                    ('class', 'maps_to', 'table'),\n", "                    ('class', 'extends', 'class'),\n", "                    ('class', 'implements', 'interface'),\n", "                    ('interface', 'extends', 'interface'),\n", "                    ('method', 'calls', 'method'),\n", "                    ('method', 'reads_from', 'table'),\n", "                    ('method', 'writes_to', 'table')\n", "                }\n", "                \n", "                if (s_type, rel_type, t_type) not in valid_directions:\n", "                    continue\n", "\n", "                all_llm_lineage.append({\n", "                    'source_node': s_node,\n", "                    'source_type': s_type,\n", "                    'destination_node': t_node,\n", "                    'destination_type': t_type,\n", "                    'relationship': rel_type,\n", "                    'file_path': file_path\n", "                })\n", "    except Exception as e:\n", "        continue\n", "\n", "df_llm_lineage = pd.DataFrame(all_llm_lineage)\n", "print(f' Stage 3 Complete: {len(df_llm_lineage)} LLM relationships extracted')"]}, {"cell_type": "code", "execution_count": 13, "id": "83112c6f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>buildtoolcontroller</td>\n", "      <td>file</td>\n", "      <td>buildtoolcontroller</td>\n", "      <td>class</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buildtoolcontroller</td>\n", "      <td>class</td>\n", "      <td>get /buildjoblist</td>\n", "      <td>endpoint</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buildtoolcontroller</td>\n", "      <td>class</td>\n", "      <td>get /builddetailshome</td>\n", "      <td>endpoint</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>buildtoolcontroller</td>\n", "      <td>class</td>\n", "      <td>get /builddetails</td>\n", "      <td>endpoint</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>buildtoolcontroller</td>\n", "      <td>class</td>\n", "      <td>get /jobslist</td>\n", "      <td>endpoint</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           source_node source_type       destination_node destination_type  \\\n", "0  buildtoolcontroller        file    buildtoolcontroller            class   \n", "1  buildtoolcontroller       class      get /buildjoblist         endpoint   \n", "2  buildtoolcontroller       class  get /builddetailshome         endpoint   \n", "3  buildtoolcontroller       class      get /builddetails         endpoint   \n", "4  buildtoolcontroller       class          get /jobslist         endpoint   \n", "\n", "  relationship                                          file_path  \n", "0     declares  C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  \n", "1     declares  C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  \n", "2     declares  C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  \n", "3     declares  C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  \n", "4     declares  C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "df_llm_lineage.head(5)"]}, {"cell_type": "code", "execution_count": 14, "id": "699498b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Combined data: 693 relationships after deduplication and cleaning\n"]}], "source": ["\n", "# ========== STAGE 4: DATA NORMALIZATION AND CLEANING ==========\n", "\n", "# Clean node names function\n", "def clean_dataframe_names(df):\n", "    \"\"\"Clean node names in dataframe\"\"\"\n", "    for col in [\"source_node\", \"destination_node\"]:\n", "        if col in df.columns:\n", "            df[col] = df[col].astype(str).apply(lambda x: re.sub(r'^(method|class|variable|field|table|endpoint):', '', x, flags=re.IGNORECASE))\n", "            df[col] = df[col].apply(lambda x: re.sub(r'\\.(java|class)$', '', x, flags=re.IGNORECASE))\n", "            df[col] = df[col].str.strip()\n", "    return df\n", "\n", "# Clean all dataframes\n", "df_folders = clean_dataframe_names(df_folders)\n", "df_files = clean_dataframe_names(df_files)\n", "df_llm_lineage = clean_dataframe_names(df_llm_lineage)\n", "\n", "# Normalize dataframes (excluding AST to reduce noise)\n", "for df in [df_folders, df_files, df_llm_lineage]:\n", "    for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "        if col in df.columns:\n", "            df[col] = df[col].astype(str).str.strip().str.lower()\n", "\n", "# Combine dataframes (EXCLUDING df_ast to reduce noise)\n", "df_combined = pd.concat([df_folders, df_files, df_llm_lineage], ignore_index=True)\n", "df_combined.drop_duplicates(\n", "    subset=[\"source_node\", \"source_type\", \"destination_node\", \"destination_type\", \"relationship\"],\n", "    inplace=True\n", ")\n", "\n", "# Remove self-references\n", "df_combined = df_combined[\n", "    ~((df_combined['source_node'] == df_combined['destination_node']) &\n", "      (df_combined['source_type'] == df_combined['destination_type']))\n", "]\n", "\n", "# Remove empty/null entries\n", "df_combined = df_combined[\n", "    (df_combined['source_node'].notna()) & (df_combined['destination_node'].notna()) &\n", "    (df_combined['source_node'] != '') & (df_combined['destination_node'] != '') &\n", "    (df_combined['source_node'] != 'none') & (df_combined['destination_node'] != 'none')\n", "]\n", "\n", "print(f' Combined data: {len(df_combined)} relationships after deduplication and cleaning')"]}, {"cell_type": "code", "execution_count": 15, "id": "67911c9c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Stage 5 Complete: 693 corrected relationships ready for Neo4j\n"]}], "source": ["\n", "# ========== STAGE 5: FILE VS CLASS CORRECTION ==========\n", "\n", "\n", "# Create a corrected dataframe for insertion\n", "df_corrected = df_combined.copy()\n", "\n", "# Fix File and Class naming for consistency\n", "for idx, row in df_corrected.iterrows():\n", "    source_node = str(row[\"source_node\"]).strip()\n", "    dest_node = str(row[\"destination_node\"]).strip()\n", "    source_type = str(row[\"source_type\"]).strip().lower()\n", "    dest_type = str(row[\"destination_type\"]).strip().lower()\n", "\n", "    # --- File Node Correction ---\n", "    if source_type == \"file\":\n", "        match = df_files[df_files[\"destination_node\"].str.replace(\".java\", \"\", case=False) == source_node.replace(\".java\", \"\")]\n", "        if not match.empty:\n", "            df_corrected.at[idx, \"source_node\"] = match.iloc[0][\"destination_node\"]\n", "\n", "    if dest_type == \"file\":\n", "        match = df_files[df_files[\"destination_node\"].str.replace(\".java\", \"\", case=False) == dest_node.replace(\".java\", \"\")]\n", "        if not match.empty:\n", "            df_corrected.at[idx, \"destination_node\"] = match.iloc[0][\"destination_node\"]\n", "\n", "    # --- Class Node Correction ---\n", "    if source_type == \"class\":\n", "        clean_name = source_node.replace(\".java\", \"\")\n", "        for class_name in class_registry.keys():\n", "            if class_name.lower() == clean_name.lower():\n", "                df_corrected.at[idx, \"source_node\"] = class_name.lower()\n", "                break\n", "\n", "    if dest_type == \"class\":\n", "        clean_name = dest_node.replace(\".java\", \"\")\n", "        for class_name in class_registry.keys():\n", "            if class_name.lower() == clean_name.lower():\n", "                df_corrected.at[idx, \"destination_node\"] = class_name.lower()\n", "                break\n", "            \n", "print(f' Stage 5 Complete: {len(df_corrected)} corrected relationships ready for Neo4j')"]}, {"cell_type": "code", "execution_count": 16, "id": "ec450174", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>setid</td>\n", "      <td>method</td>\n", "      <td>id</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>buildtoolcontroller</td>\n", "      <td>class</td>\n", "      <td>get /jenkinsbuildfailuredata</td>\n", "      <td>endpoint</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>299</th>\n", "      <td>buildfileinfo</td>\n", "      <td>class</td>\n", "      <td>edittype</td>\n", "      <td>variable</td>\n", "      <td>has_field</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>794</th>\n", "      <td>githubactionapplication</td>\n", "      <td>class</td>\n", "      <td>pagelimit</td>\n", "      <td>variable</td>\n", "      <td>has_field</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>413</th>\n", "      <td>geturl</td>\n", "      <td>method</td>\n", "      <td>url</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 source_node source_type              destination_node  \\\n", "250                    setid      method                            id   \n", "33       buildtoolcontroller       class  get /jenkinsbuildfailuredata   \n", "299            buildfileinfo       class                      edittype   \n", "794  githubactionapplication       class                     pagelimit   \n", "413                   geturl      method                           url   \n", "\n", "    destination_type relationship  \\\n", "250         variable         uses   \n", "33          endpoint     declares   \n", "299         variable    has_field   \n", "794         variable    has_field   \n", "413         variable         uses   \n", "\n", "                                             file_path  \n", "250  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...  \n", "33   C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  \n", "299  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...  \n", "794  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...  \n", "413  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined.sample(5)"]}, {"cell_type": "code", "execution_count": 17, "id": "6e72851d", "metadata": {}, "outputs": [], "source": ["df_combined.to_csv('combined_data.csv', index=False)"]}, {"cell_type": "code", "execution_count": 18, "id": "6ec998a3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Final Neo4j Insertion: 100%|██████████| 693/693 [00:11<00:00, 60.23it/s] "]}, {"name": "stdout", "output_type": "stream", "text": [" Final consistent data pushed to Neo4j.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 6: NEO4J INSERTION ==========\n", "\n", "# Clear existing data\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n", "\n", "# Insert clean corrected data\n", "for idx, row in tqdm(df_corrected.iterrows(), total=len(df_corrected), desc=\"Final Neo4j Insertion\"):\n", "    try:\n", "        source_node = str(row[\"source_node\"]).strip()\n", "        dest_node = str(row[\"destination_node\"]).strip()\n", "        source_type = str(row[\"source_type\"]).strip().capitalize()\n", "        dest_type = str(row[\"destination_type\"]).strip().capitalize()\n", "        relationship = str(row[\"relationship\"]).strip().upper()\n", "\n", "        if not all([source_node, dest_node, source_type, dest_type, relationship]):\n", "            continue\n", "\n", "        query = f\"\"\"\n", "        MERGE (s:{source_type} {{name: $source_node}})\n", "        MERGE (t:{dest_type} {{name: $destination_node}})\n", "        MERGE (s)-[:{relationship}]->(t)\n", "        \"\"\"\n", "        graph.query(query, {\n", "            \"source_node\": source_node,\n", "            \"destination_node\": dest_node\n", "        })\n", "    except Exception as e:\n", "        pass  # Optionally collect failed rows for review\n", "\n", "print(\" Final consistent data pushed to Neo4j.\")"]}, {"cell_type": "code", "execution_count": 19, "id": "b97c40a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total Nodes in Neo4j: 419\n", "Total Relationships in Neo4j: 693\n", "\n", " Node Types:\n", "  Variable: 183\n", "  Method: 170\n", "  File: 19\n", "  Class: 14\n", "  Endpoint: 12\n", "  Folder: 10\n", "  Interface: 7\n", "  Table: 4\n", "\n", " Relationship Types:\n", "  USES: 293\n", "  DECLARES: 235\n", "  HAS_FIELD: 116\n", "  CONTAINS: 27\n", "  CALLS: 9\n", "  EXTENDS: 5\n", "  IMPLEMENTS: 3\n", "  MAPS_TO: 3\n", "  READS_FROM: 1\n", "  WRITES_TO: 1\n", "\n", " Sample API Endpoints:\n", "  get /buildjoblist\n", "  get /builddetailshome\n", "  get /builddetails\n", "  get /jobslist\n", "  get /jenkinsbuildfailure\n", "\n", "Sample Database Tables:\n", "  buildfailurepatternforproject\n", "  build\n", "  configuration\n", "  buildfailurepatternforprojectinjenkinsmodel\n"]}], "source": ["\n", "# ========== VALIDATION ==========\n", "def validate_graph_statistics():\n", "    node_count = graph.query(\"MATCH (n) RETURN count(n) as count\")[0][\"count\"]\n", "    rel_count = graph.query(\"MATCH ()-[r]->() RETURN count(r) as count\")[0][\"count\"]\n", "\n", "    print(f\"Total Nodes in Neo4j: {node_count}\")\n", "    print(f\"Total Relationships in Neo4j: {rel_count}\")\n", "    \n", "    # Show breakdown by node type\n", "    node_types = graph.query(\"MATCH (n) RETURN labels(n)[0] as type, count(n) as count ORDER BY count DESC\")\n", "    print('\\n Node Types:')\n", "    for row in node_types:\n", "        print(f'  {row[\"type\"]}: {row[\"count\"]}')\n", "    \n", "    # Show breakdown by relationship type\n", "    rel_types = graph.query(\"MATCH ()-[r]->() RETURN type(r) as type, count(r) as count ORDER BY count DESC\")\n", "    print('\\n Relationship Types:')\n", "    for row in rel_types:\n", "        print(f'  {row[\"type\"]}: {row[\"count\"]}')\n", "    \n", "    # Show sample endpoints and tables\n", "    endpoints = graph.query(\"MATCH (n:Endpoint) RETURN n.name as name LIMIT 5\")\n", "    if endpoints:\n", "        print('\\n Sample API Endpoints:')\n", "        for ep in endpoints:\n", "            print(f'  {ep[\"name\"]}')\n", "    \n", "    tables = graph.query(\"MATCH (n:Table) RETURN n.name as name LIMIT 5\")\n", "    if tables:\n", "        print('\\nSample Database Tables:')\n", "        for table in tables:\n", "            print(f'  {table[\"name\"]}')\n", "\n", "validate_graph_statistics()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c4d9b930", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.read_csv('combined_data.csv')"]}, {"cell_type": "code", "execution_count": 6, "id": "42dbbc7b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>302</th>\n", "      <td>buildtool</td>\n", "      <td>class</td>\n", "      <td>getjenkinsitemid</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>oneinsights</td>\n", "      <td>folder</td>\n", "      <td>servicebolt</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>657</th>\n", "      <td>processpipelinedata</td>\n", "      <td>method</td>\n", "      <td>started</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>602</th>\n", "      <td>githubactionimplementation</td>\n", "      <td>class</td>\n", "      <td>pipelineid</td>\n", "      <td>variable</td>\n", "      <td>has_field</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>buildtoolcontroller</td>\n", "      <td>class</td>\n", "      <td>gitlabbuilddata</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    source_node source_type  destination_node  \\\n", "302                   buildtool       class  getjenkinsitemid   \n", "0                   oneinsights      folder       servicebolt   \n", "657         processpipelinedata      method           started   \n", "602  githubactionimplementation       class        pipelineid   \n", "52          buildtoolcontroller       class   gitlabbuilddata   \n", "\n", "    destination_type relationship  \\\n", "302           method     declares   \n", "0             folder     contains   \n", "657         variable         uses   \n", "602         variable    has_field   \n", "52            method     declares   \n", "\n", "                                             file_path  \n", "302  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\m...  \n", "0                                                  NaN  \n", "657  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...  \n", "602  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...  \n", "52   C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.sample(5)"]}, {"cell_type": "code", "execution_count": 20, "id": "bf52ca89", "metadata": {}, "outputs": [], "source": ["filtered_df = df[(df['source_type'] == 'variable') | (df['destination_type'] == 'variable')]"]}, {"cell_type": "code", "execution_count": 21, "id": "19b7feda", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>buildtoolcontroller</td>\n", "      <td>class</td>\n", "      <td>buildtoolservice</td>\n", "      <td>variable</td>\n", "      <td>has_field</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>joblist</td>\n", "      <td>method</td>\n", "      <td>buildtype</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>savebuildfailurepattern</td>\n", "      <td>method</td>\n", "      <td>obj</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>fetchfailurepatterndata</td>\n", "      <td>method</td>\n", "      <td>obj</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>fetchfailurepatterndatacopy</td>\n", "      <td>method</td>\n", "      <td>obj</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>682</th>\n", "      <td>createheaders</td>\n", "      <td>method</td>\n", "      <td>authheader</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>683</th>\n", "      <td>createheaders</td>\n", "      <td>method</td>\n", "      <td>headers</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>685</th>\n", "      <td>get</td>\n", "      <td>method</td>\n", "      <td>requestfactory</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>688</th>\n", "      <td>processpipelinedata</td>\n", "      <td>method</td>\n", "      <td>logger</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>689</th>\n", "      <td>processfailure</td>\n", "      <td>method</td>\n", "      <td>logger</td>\n", "      <td>variable</td>\n", "      <td>uses</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>409 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                     source_node source_type  destination_node  \\\n", "39           buildtoolcontroller       class  buildtoolservice   \n", "44                       joblist      method         buildtype   \n", "46       savebuildfailurepattern      method               obj   \n", "49       fetchfailurepatterndata      method               obj   \n", "51   fetchfailurepatterndatacopy      method               obj   \n", "..                           ...         ...               ...   \n", "682                createheaders      method        authheader   \n", "683                createheaders      method           headers   \n", "685                          get      method    requestfactory   \n", "688          processpipelinedata      method            logger   \n", "689               processfailure      method            logger   \n", "\n", "    destination_type relationship  \\\n", "39          variable    has_field   \n", "44          variable         uses   \n", "46          variable         uses   \n", "49          variable         uses   \n", "51          variable         uses   \n", "..               ...          ...   \n", "682         variable         uses   \n", "683         variable         uses   \n", "685         variable         uses   \n", "688         variable         uses   \n", "689         variable         uses   \n", "\n", "                                             file_path  \n", "39   C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  \n", "44   C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  \n", "46   C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  \n", "49   C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  \n", "51   C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\Bu...  \n", "..                                                 ...  \n", "682  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...  \n", "683  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...  \n", "685  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...  \n", "688  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...  \n", "689  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\github...  \n", "\n", "[409 rows x 6 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_df"]}, {"cell_type": "code", "execution_count": 9, "id": "b3383e29", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def normalize_nodes(df):\n", "    \"\"\"\n", "    Normalize the source_node and destination_node by taking only the last segment \n", "    if path-like strings are present.\n", "    \"\"\"\n", "    df = df.copy()\n", "    for col in [\"source_node\", \"destination_node\"]:\n", "        df[col] = df[col].astype(str).apply(lambda x: x.replace(\"\\\\\", \"/\").split(\"/\")[-1])\n", "    return df\n"]}, {"cell_type": "code", "execution_count": 12, "id": "4daf57b5", "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "\n", "def push_to_neo4j(df_cleaned, graph):\n", "    \"\"\"\n", "    <PERSON><PERSON> cleaned DataFrame to Neo4j after clearing existing data.\n", "    \"\"\"\n", "    # Clear existing data\n", "    graph.query(\"MATCH (n) DETACH DELETE n\")\n", "\n", "    for idx, row in tqdm(df_cleaned.iterrows(), total=len(df_cleaned), desc=\"Final Neo4j Insertion\"):\n", "        try:\n", "            source_node = str(row[\"source_node\"]).strip()\n", "            dest_node = str(row[\"destination_node\"]).strip()\n", "            source_type = str(row[\"source_type\"]).strip().capitalize()\n", "            dest_type = str(row[\"destination_type\"]).strip().capitalize()\n", "            relationship = str(row[\"relationship\"]).strip().upper()\n", "\n", "            if not all([source_node, dest_node, source_type, dest_type, relationship]):\n", "                continue\n", "\n", "            query = f\"\"\"\n", "            MERGE (s:{source_type} {{name: $source_node}})\n", "            MERGE (t:{dest_type} {{name: $destination_node}})\n", "            MERGE (s)-[:{relationship}]->(t)\n", "            \"\"\"\n", "            graph.query(query, {\n", "                \"source_node\": source_node,\n", "                \"destination_node\": dest_node\n", "            })\n", "        except Exception as e:\n", "            pass  # You may want to log this for debugging\n", "\n", "    print(\"✅ Final consistent data pushed to Neo4j.\")\n"]}, {"cell_type": "code", "execution_count": 14, "id": "12ad0d40", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Final Neo4j Insertion: 100%|██████████| 693/693 [00:05<00:00, 133.78it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Final consistent data pushed to Neo4j.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Step 1: Normalize the CSV DataFrame\n", "df_corrected = normalize_nodes(df)\n", "\n", "# Step 2: <PERSON><PERSON> to <PERSON>4j\n", "push_to_neo4j(df_corrected, graph)\n"]}, {"cell_type": "code", "execution_count": 17, "id": "2d6abe32", "metadata": {}, "outputs": [], "source": ["filtered_df = df_corrected[(df_corrected['source_type'] == 'folder')]"]}, {"cell_type": "code", "execution_count": 18, "id": "2587ddd7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>oneinsights</td>\n", "      <td>folder</td>\n", "      <td>servicebolt</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>servicebolt</td>\n", "      <td>folder</td>\n", "      <td>api</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>servicebolt</td>\n", "      <td>folder</td>\n", "      <td>service</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>oneinsights</td>\n", "      <td>folder</td>\n", "      <td>unifiedbolt</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>unifiedbolt</td>\n", "      <td>folder</td>\n", "      <td>core</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>core</td>\n", "      <td>folder</td>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>core</td>\n", "      <td>folder</td>\n", "      <td>repository</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>unifiedbolt</td>\n", "      <td>folder</td>\n", "      <td>githubaction</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>api</td>\n", "      <td>folder</td>\n", "      <td>buildtoolcontroller</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>ServiceBolt\\api\\BuildToolController.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>service</td>\n", "      <td>folder</td>\n", "      <td>buildtoolservice</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>ServiceBolt\\service\\BuildToolService.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>service</td>\n", "      <td>folder</td>\n", "      <td>buildtoolserviceimplemantation</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>ServiceBolt\\service\\BuildToolServiceImplemanta...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>basemodel</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\BaseModel.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>buildfailurepatternforprojectinjenkinsmodel</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildFailurePatternForP...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>buildfailurepatternmetrics</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildFailurePatternMetr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>buildfileinfo</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildFileInfo.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>buildinfo</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildInfo.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>buildsteps</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildSteps.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>buildtool</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildTool.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>buildtoolmetric</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildToolMetric.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>configurationsetting</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\ConfigurationSetting.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>configurationtoolinfometric</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\model\\ConfigurationToolInfoMe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>repository</td>\n", "      <td>folder</td>\n", "      <td>buildfailurepatternforprojectrepo</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\repository\\BuildFailurePatter...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>repository</td>\n", "      <td>folder</td>\n", "      <td>buildtoolrep</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\repository\\BuildToolRep.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>repository</td>\n", "      <td>folder</td>\n", "      <td>configurationsettingrep</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\core\\repository\\ConfigurationSetti...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>githubaction</td>\n", "      <td>folder</td>\n", "      <td>githubaction</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubAction.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>githubaction</td>\n", "      <td>folder</td>\n", "      <td>githubactionapplication</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionApplicati...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>githubaction</td>\n", "      <td>folder</td>\n", "      <td>githubactionimplementation</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     source_node source_type                             destination_node  \\\n", "0    oneinsights      folder                                  servicebolt   \n", "1    servicebolt      folder                                          api   \n", "2    servicebolt      folder                                      service   \n", "3    oneinsights      folder                                  unifiedbolt   \n", "4    unifiedbolt      folder                                         core   \n", "5           core      folder                                        model   \n", "6           core      folder                                   repository   \n", "7    unifiedbolt      folder                                 githubaction   \n", "8            api      folder                          buildtoolcontroller   \n", "9        service      folder                             buildtoolservice   \n", "10       service      folder               buildtoolserviceimplemantation   \n", "11         model      folder                                    basemodel   \n", "12         model      folder  buildfailurepatternforprojectinjenkinsmodel   \n", "13         model      folder                   buildfailurepatternmetrics   \n", "14         model      folder                                buildfileinfo   \n", "15         model      folder                                    buildinfo   \n", "16         model      folder                                   buildsteps   \n", "17         model      folder                                    buildtool   \n", "18         model      folder                              buildtoolmetric   \n", "19         model      folder                         configurationsetting   \n", "20         model      folder                  configurationtoolinfometric   \n", "21    repository      folder            buildfailurepatternforprojectrepo   \n", "22    repository      folder                                 buildtoolrep   \n", "23    repository      folder                      configurationsettingrep   \n", "24  githubaction      folder                                 githubaction   \n", "25  githubaction      folder                      githubactionapplication   \n", "26  githubaction      folder                   githubactionimplementation   \n", "\n", "   destination_type relationship  \\\n", "0            folder     contains   \n", "1            folder     contains   \n", "2            folder     contains   \n", "3            folder     contains   \n", "4            folder     contains   \n", "5            folder     contains   \n", "6            folder     contains   \n", "7            folder     contains   \n", "8              file     contains   \n", "9              file     contains   \n", "10             file     contains   \n", "11             file     contains   \n", "12             file     contains   \n", "13             file     contains   \n", "14             file     contains   \n", "15             file     contains   \n", "16             file     contains   \n", "17             file     contains   \n", "18             file     contains   \n", "19             file     contains   \n", "20             file     contains   \n", "21             file     contains   \n", "22             file     contains   \n", "23             file     contains   \n", "24             file     contains   \n", "25             file     contains   \n", "26             file     contains   \n", "\n", "                                            file_path  \n", "0                                                 NaN  \n", "1                                                 NaN  \n", "2                                                 NaN  \n", "3                                                 NaN  \n", "4                                                 NaN  \n", "5                                                 NaN  \n", "6                                                 NaN  \n", "7                                                 NaN  \n", "8            ServiceBolt\\api\\BuildToolController.java  \n", "9           ServiceBolt\\service\\BuildToolService.java  \n", "10  ServiceBolt\\service\\BuildToolServiceImplemanta...  \n", "11              UnifiedBolt\\core\\model\\BaseModel.java  \n", "12  UnifiedBolt\\core\\model\\BuildFailurePatternForP...  \n", "13  UnifiedBolt\\core\\model\\BuildFailurePatternMetr...  \n", "14          UnifiedBolt\\core\\model\\BuildFileInfo.java  \n", "15              UnifiedBolt\\core\\model\\BuildInfo.java  \n", "16             UnifiedBolt\\core\\model\\BuildSteps.java  \n", "17              UnifiedBolt\\core\\model\\BuildTool.java  \n", "18        UnifiedBolt\\core\\model\\BuildToolMetric.java  \n", "19   UnifiedBolt\\core\\model\\ConfigurationSetting.java  \n", "20  UnifiedBolt\\core\\model\\ConfigurationToolInfoMe...  \n", "21  UnifiedBolt\\core\\repository\\BuildFailurePatter...  \n", "22      UnifiedBolt\\core\\repository\\BuildToolRep.java  \n", "23  UnifiedBolt\\core\\repository\\ConfigurationSetti...  \n", "24         UnifiedBolt\\githubaction\\GithubAction.java  \n", "25  UnifiedBolt\\githubaction\\GithubActionApplicati...  \n", "26  UnifiedBolt\\githubaction\\GithubActionImplement...  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_df"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}