# ========== STAGE 1: SETUP AND CONFIGURATION ==========

import os
import pandas as pd
import re
import json
from pathlib import Path
from tqdm import tqdm
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# Tree-sitter for AST parsing
from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

# LangChain for LLM processing
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph

# ========== CONFIGURATION ==========
BASE_PATH = Path(r'C:/Shaik/sample/OneInsights')
NEO4J_URI = 'bolt://localhost:7687'
NEO4J_USER = 'neo4j'
NEO4J_PASSWORD = 'Test@7889'
NEO4J_DB = 'oneinsights-v11'
GOOGLE_API_KEY = 'AIzaSyD29vmAqN-z2ZlJ4vJ5YGEHoQo_79UqXQM'

# ========== INITIALIZE COMPONENTS ==========
print('🔧 Initializing components...')

# Neo4j connection
try:
    graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
    print('✅ Neo4j connection established')
except Exception as e:
    print(f'❌ Neo4j connection failed: {e}')
    graph = None

# Tree-sitter Java parser
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

# LLM for enhanced extraction
llm = ChatGoogleGenerativeAI(
    model='gemini-2.0-flash-exp',
    temperature=0,
    google_api_key=GOOGLE_API_KEY
)

# ========== APPLICATION MAPPING ==========
APPLICATIONS = {
    'ServiceBolt': 'REST API Service Layer',
    'UnifiedBolt': 'Core Business Logic and Data Layer'
}

# ========== NOISE FILTERING (Global Variables Only) ==========
NOISE_VARIABLES = {
    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',
    'temp', 'tmp', 'obj', 'item', 'elem', 'node', 'val', 'value',
    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',
    'this', 'super', 'null', 'true', 'false', 'void', 'return',
    'it', 'ex', 'e1', 'e2', 'o1', 'o2'
}

def is_meaningful_variable(var_name):
    '''Filter meaningful variables only (global scope focus)'''
    if not var_name or len(var_name) < 3:
        return False
    if var_name.lower() in NOISE_VARIABLES:
        return False
    if re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', var_name):
        return True
    return False

def is_global_variable(var_name, context):
    '''Check if variable is global (class-level field or service injection)'''
    # Global indicators
    global_patterns = [
        r'@Autowired',
        r'@Inject',
        r'@Resource',
        r'private\s+\w+\s+' + re.escape(var_name),
        r'protected\s+\w+\s+' + re.escape(var_name),
        r'public\s+\w+\s+' + re.escape(var_name),
        r'static\s+\w+\s+' + re.escape(var_name)
    ]
    
    for pattern in global_patterns:
        if re.search(pattern, context, re.IGNORECASE):
            return True
    
    return False

# ========== METADATA STRUCTURES ==========
class MetadataCollector:
    def __init__(self):
        self.class_metadata = {}     # Class metadata with inheritance
        self.method_metadata = {}    # Method metadata with signatures
        self.variable_metadata = {}  # Global variable metadata with transformations
        self.file_metadata = {}      # File metadata with imports/packages
        self.folder_metadata = {}    # Folder metadata with structure
        self.app_metadata = {}       # Application metadata with tech stack
    
    def add_class_metadata(self, class_name, file_path, app_name, **kwargs):
        '''Add comprehensive class metadata'''
        self.class_metadata[class_name] = {
            'file_path': file_path,
            'application': app_name,
            'parent_class': kwargs.get('parent_class'),
            'child_classes': kwargs.get('child_classes', []),
            'interfaces': kwargs.get('interfaces', []),
            'annotations': kwargs.get('annotations', []),
            'package': kwargs.get('package'),
            'access_modifier': kwargs.get('access_modifier', 'public'),
            'is_abstract': kwargs.get('is_abstract', False),
            'is_final': kwargs.get('is_final', False),
            'field_count': kwargs.get('field_count', 0),
            'method_count': kwargs.get('method_count', 0)
        }
    
    def add_method_metadata(self, method_key, class_name, file_path, app_name, **kwargs):
        '''Add comprehensive method metadata'''
        self.method_metadata[method_key] = {
            'class': class_name,
            'file_path': file_path,
            'application': app_name,
            'return_type': kwargs.get('return_type'),
            'parameters': kwargs.get('parameters', []),
            'annotations': kwargs.get('annotations', []),
            'access_modifier': kwargs.get('access_modifier', 'public'),
            'is_static': kwargs.get('is_static', False),
            'is_abstract': kwargs.get('is_abstract', False),
            'is_final': kwargs.get('is_final', False),
            'throws_exceptions': kwargs.get('throws_exceptions', []),
            'variable_count': kwargs.get('variable_count', 0)
        }
    
    def add_variable_metadata(self, var_key, class_name, method_name, file_path, app_name, **kwargs):
        '''Add comprehensive global variable metadata'''
        self.variable_metadata[var_key] = {
            'original_name': kwargs.get('original_name'),
            'class': class_name,
            'method': method_name,
            'file_path': file_path,
            'application': app_name,
            'definition_location': kwargs.get('definition_location'),
            'storage_type': kwargs.get('storage_type', 'field'),  # field, parameter, local
            'data_type': kwargs.get('data_type'),
            'access_modifier': kwargs.get('access_modifier'),
            'is_static': kwargs.get('is_static', False),
            'is_final': kwargs.get('is_final', False),
            'annotations': kwargs.get('annotations', []),
            'transformations': kwargs.get('transformations', []),
            'scope': kwargs.get('scope', 'global'),
            'usage_count': kwargs.get('usage_count', 0)
        }

# Initialize metadata collector
metadata = MetadataCollector()

# ========== GLOBAL DATA STRUCTURES ==========
all_relationships = []  # Master list of all relationships
class_registry = {}     # Track all classes and their details
method_registry = {}    # Track all methods and their details
variable_registry = {}  # Track all global variables and their context

print('🚀 Setup complete! Ready for metadata-enhanced v12 analysis...')

# ========== STAGE 2: FILE DISCOVERY AND HIERARCHY BUILDING WITH METADATA ==========

def extract_package_from_file(file_path):
    '''Extract package name from Java file'''
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            package_match = re.search(r'package\s+([\w\.]+);', content)
            if package_match:
                return package_match.group(1)
    except Exception:
        pass
    return None

def extract_imports_from_file(file_path):
    '''Extract import statements from Java file'''
    imports = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            import_matches = re.findall(r'import\s+([\w\.\*]+);', content)
            imports = import_matches
    except Exception:
        pass
    return imports

def extract_class_count_from_file(file_path):
    '''Extract number of classes in a Java file'''
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            class_matches = re.findall(r'(public|private|protected)?\s*(abstract|final)?\s*class\s+([\w<>]+)', content)
            return len(class_matches)
    except Exception:
        pass
    return 0

def build_project_hierarchy_with_metadata():
    '''Build complete project hierarchy with rich metadata'''
    print('🏗️ Building project hierarchy with metadata...')
    
    hierarchy_relationships = []
    
    # PROJECT level with metadata
    project_name = 'OneInsights'
    project_metadata = {
        'description': 'Unified insights platform for data analysis',
        'applications': list(APPLICATIONS.keys()),
        'version': '1.0.0'
    }
    
    # APPLICATION level - Connect project to BOTH applications with metadata
    for app_name, app_desc in APPLICATIONS.items():
        # Add application metadata
        app_tech_stack = {
            'ServiceBolt': ['Spring Boot', 'REST API', 'Spring Security', 'Spring Data'],
            'UnifiedBolt': ['Core Java', 'JPA/Hibernate', 'Business Logic']
        }
        
        metadata.app_metadata[app_name] = {
            'description': app_desc,
            'technology_stack': app_tech_stack.get(app_name, []),
            'dependencies': [],
            'api_endpoints': []
        }
        
        # Create relationship with metadata
        hierarchy_relationships.append({
            'source_node': project_name,
            'source_type': 'project',
            'destination_node': app_name,
            'destination_type': 'application',
            'relationship': 'contains',
            'file_path': None,
            'application': app_name,
            'metadata': json.dumps({
                'project_metadata': project_metadata,
                'application_metadata': metadata.app_metadata[app_name]
            })
        })
    
    # FOLDER and FILE level with metadata
    for app_name in APPLICATIONS.keys():
        app_path = BASE_PATH / app_name
        if not app_path.exists():
            print(f'⚠️ Application path not found: {app_path}')
            continue
        
        print(f'📁 Processing application: {app_name}')
        
        # Find all Java files
        java_files = list(app_path.rglob('*.java'))
        print(f'   Found {len(java_files)} Java files')
        
        folders_processed = set()
        folder_file_counts = defaultdict(int)
        folder_class_counts = defaultdict(int)
        
        # First pass: collect folder statistics
        for java_file in java_files:
            relative_path = java_file.relative_to(app_path)
            folder = str(relative_path.parent)
            folder_file_counts[folder] += 1
            folder_class_counts[folder] += extract_class_count_from_file(java_file)
        
        # Second pass: build hierarchy with metadata
        for java_file in java_files:
            relative_path = java_file.relative_to(app_path)
            path_parts = relative_path.parts
            
            # Extract file metadata
            package_name = extract_package_from_file(java_file)
            imports = extract_imports_from_file(java_file)
            class_count = extract_class_count_from_file(java_file)
            
            file_metadata = {
                'package_name': package_name,
                'imports': imports,
                'class_count': class_count,
                'file_size': os.path.getsize(java_file),
                'last_modified': os.path.getmtime(java_file)
            }
            
            metadata.file_metadata[str(java_file)] = file_metadata
            
            # Build folder hierarchy
            current_parent = app_name
            current_parent_type = 'application'
            
            # Process each folder in the path
            for i, folder in enumerate(path_parts[:-1]):  # Exclude the file itself
                folder_key = '/'.join(path_parts[:i+1])
                folder_path = str(app_path / '/'.join(path_parts[:i+1]))
                
                if folder_key not in folders_processed:
                    # Create folder metadata
                    folder_metadata = {
                        'file_count': folder_file_counts.get(folder_key, 0),
                        'class_count': folder_class_counts.get(folder_key, 0),
                        'package_structure': package_name.split('.')[:(i+1)] if package_name else [],
                        'depth': i + 1
                    }
                    
                    metadata.folder_metadata[folder_key] = folder_metadata
                    
                    # APPLICATION/FOLDER → FOLDER relationship with metadata
                    hierarchy_relationships.append({
                        'source_node': current_parent,
                        'source_type': current_parent_type,
                        'destination_node': folder,
                        'destination_type': 'folder',
                        'relationship': 'contains',
                        'file_path': folder_path,
                        'application': app_name,
                        'metadata': json.dumps(folder_metadata)
                    })
                    folders_processed.add(folder_key)
                
                current_parent = folder
                current_parent_type = 'folder'
            
            # FOLDER → FILE relationship with metadata
            file_name = java_file.stem  # Without .java extension
            hierarchy_relationships.append({
                'source_node': current_parent,
                'source_type': current_parent_type,
                'destination_node': file_name,
                'destination_type': 'file',
                'relationship': 'contains',
                'file_path': str(java_file),
                'application': app_name,
                'metadata': json.dumps(file_metadata)
            })
    
    print(f'✅ Built hierarchy with {len(hierarchy_relationships)} relationships and rich metadata')
    return hierarchy_relationships

# Build the hierarchy with metadata
hierarchy_rels = build_project_hierarchy_with_metadata()
all_relationships.extend(hierarchy_rels)

print(f'📊 Current total relationships: {len(all_relationships)}')

# ========== STAGE 3: AST-BASED CODE ANALYSIS WITH RICH METADATA ==========

def extract_class_info_with_metadata(node, source_code):
    '''Extract comprehensive class information with metadata'''
    class_name = None
    extends_class = None
    interfaces = []
    annotations = []
    access_modifier = 'public'
    is_abstract = False
    is_final = False
    
    # Extract class modifiers and annotations
    for child in node.children:
        if child.type == 'identifier':
            class_name = source_code[child.start_byte:child.end_byte]
        elif child.type == 'superclass':
            for subchild in child.children:
                if subchild.type == 'type_identifier':
                    extends_class = source_code[subchild.start_byte:subchild.end_byte]
        elif child.type == 'super_interfaces':
            for subchild in child.children:
                if subchild.type == 'type_identifier':
                    interfaces.append(source_code[subchild.start_byte:subchild.end_byte])
        elif child.type == 'modifiers':
            modifiers_text = source_code[child.start_byte:child.end_byte]
            if 'private' in modifiers_text:
                access_modifier = 'private'
            elif 'protected' in modifiers_text:
                access_modifier = 'protected'
            is_abstract = 'abstract' in modifiers_text
            is_final = 'final' in modifiers_text
    
    # Extract annotations (look for preceding annotation nodes)
    parent = node.parent
    if parent:
        for sibling in parent.children:
            if sibling.type == 'annotation' and sibling.start_byte < node.start_byte:
                annotation_text = source_code[sibling.start_byte:sibling.end_byte]
                annotations.append(annotation_text)
    
    return {
        'class_name': class_name,
        'parent_class': extends_class,
        'interfaces': interfaces,
        'annotations': annotations,
        'access_modifier': access_modifier,
        'is_abstract': is_abstract,
        'is_final': is_final
    }

def extract_method_info_with_metadata(node, source_code):
    '''Extract comprehensive method information with metadata'''
    method_name = None
    parameters = []
    return_type = None
    annotations = []
    access_modifier = 'public'
    is_static = False
    is_abstract = False
    is_final = False
    throws_exceptions = []
    
    for child in node.children:
        if child.type == 'identifier':
            method_name = source_code[child.start_byte:child.end_byte]
        elif child.type == 'type_identifier' or child.type == 'generic_type':
            return_type = source_code[child.start_byte:child.end_byte]
        elif child.type == 'formal_parameters':
            for param_child in child.children:
                if param_child.type == 'formal_parameter':
                    param_info = {'name': None, 'type': None}
                    for param_part in param_child.children:
                        if param_part.type == 'variable_declarator':
                            for var_part in param_part.children:
                                if var_part.type == 'identifier':
                                    param_info['name'] = source_code[var_part.start_byte:var_part.end_byte]
                        elif param_part.type in ['type_identifier', 'generic_type']:
                            param_info['type'] = source_code[param_part.start_byte:param_part.end_byte]
                    if param_info['name'] and is_meaningful_variable(param_info['name']):
                        parameters.append(param_info)
        elif child.type == 'modifiers':
            modifiers_text = source_code[child.start_byte:child.end_byte]
            if 'private' in modifiers_text:
                access_modifier = 'private'
            elif 'protected' in modifiers_text:
                access_modifier = 'protected'
            is_static = 'static' in modifiers_text
            is_abstract = 'abstract' in modifiers_text
            is_final = 'final' in modifiers_text
        elif child.type == 'throws':
            for exception_child in child.children:
                if exception_child.type == 'type_identifier':
                    throws_exceptions.append(source_code[exception_child.start_byte:exception_child.end_byte])
    
    # Extract annotations
    parent = node.parent
    if parent:
        for sibling in parent.children:
            if sibling.type == 'annotation' and sibling.start_byte < node.start_byte:
                annotation_text = source_code[sibling.start_byte:sibling.end_byte]
                annotations.append(annotation_text)
    
    return {
        'method_name': method_name,
        'return_type': return_type,
        'parameters': parameters,
        'annotations': annotations,
        'access_modifier': access_modifier,
        'is_static': is_static,
        'is_abstract': is_abstract,
        'is_final': is_final,
        'throws_exceptions': throws_exceptions
    }

def extract_global_variables_with_metadata(node, source_code, class_name, method_context=None):
    '''Extract global variables with comprehensive metadata'''
    variables = []
    
    def traverse_for_fields(n, is_class_level=False):
        '''Traverse to find field declarations (global variables)'''
        if n.type == 'field_declaration':
            # This is a class-level field (global variable)
            field_info = extract_field_metadata(n, source_code)
            if field_info and field_info['name']:
                variables.append(field_info)
        
        # Only traverse class body for fields, not method bodies
        if n.type in ['class_body', 'class_declaration']:
            for child in n.children:
                traverse_for_fields(child, True)
    
    traverse_for_fields(node)
    return variables

def extract_field_metadata(node, source_code):
    '''Extract field metadata from field_declaration node'''
    field_name = None
    data_type = None
    access_modifier = 'private'  # Default for fields
    is_static = False
    is_final = False
    annotations = []
    
    for child in node.children:
        if child.type == 'variable_declarator':
            for var_child in child.children:
                if var_child.type == 'identifier':
                    field_name = source_code[var_child.start_byte:var_child.end_byte]
        elif child.type in ['type_identifier', 'generic_type']:
            data_type = source_code[child.start_byte:child.end_byte]
        elif child.type == 'modifiers':
            modifiers_text = source_code[child.start_byte:child.end_byte]
            if 'public' in modifiers_text:
                access_modifier = 'public'
            elif 'protected' in modifiers_text:
                access_modifier = 'protected'
            is_static = 'static' in modifiers_text
            is_final = 'final' in modifiers_text
    
    # Extract annotations
    parent = node.parent
    if parent:
        for sibling in parent.children:
            if sibling.type == 'annotation' and sibling.start_byte < node.start_byte:
                annotation_text = source_code[sibling.start_byte:sibling.end_byte]
                annotations.append(annotation_text)
    
    return {
        'name': field_name,
        'data_type': data_type,
        'access_modifier': access_modifier,
        'is_static': is_static,
        'is_final': is_final,
        'annotations': annotations,
        'storage_type': 'field'
    }

print('🔍 Starting AST analysis with rich metadata...')
ast_relationships = []

for app_name in APPLICATIONS.keys():
    app_path = BASE_PATH / app_name
    if app_path.exists():
        java_files = list(app_path.rglob('*.java'))
        print(f'📁 Processing {len(java_files)} files in {app_name}...')
        
        for java_file in tqdm(java_files, desc=f'AST Analysis - {app_name}'):
            try:
                with open(java_file, 'r', encoding='utf-8') as f:
                    source_code = f.read()
            except Exception as e:
                print(f'❌ Error reading {java_file}: {e}')
                continue
            
            tree = parser.parse(bytes(source_code, 'utf8'))
            file_name = Path(java_file).stem
            current_class = None
            
            def traverse_ast_with_metadata(node, depth=0):
                nonlocal current_class
                
                if node.type == 'class_declaration':
                    class_info = extract_class_info_with_metadata(node, source_code)
                    class_name = class_info['class_name']
                    
                    if class_name:
                        current_class = class_name
                        
                        # Add comprehensive class metadata
                        metadata.add_class_metadata(
                            class_name, str(java_file), app_name,
                            parent_class=class_info['parent_class'],
                            interfaces=class_info['interfaces'],
                            annotations=class_info['annotations'],
                            access_modifier=class_info['access_modifier'],
                            is_abstract=class_info['is_abstract'],
                            is_final=class_info['is_final'],
                            package=metadata.file_metadata.get(str(java_file), {}).get('package_name')
                        )
                        
                        # Register class
                        class_registry[class_name] = {
                            'file_path': str(java_file),
                            'application': app_name,
                            'extends': class_info['parent_class']
                        }
                        
                        # FILE → CLASS relationship with metadata
                        ast_relationships.append({
                            'source_node': file_name,
                            'source_type': 'file',
                            'destination_node': class_name,
                            'destination_type': 'class',
                            'relationship': 'contains',
                            'file_path': str(java_file),
                            'application': app_name,
                            'metadata': json.dumps(class_info)
                        })
                        
                        # CLASS → EXTENDS relationship with metadata
                        if class_info['parent_class']:
                            ast_relationships.append({
                                'source_node': class_name,
                                'source_type': 'class',
                                'destination_node': class_info['parent_class'],
                                'destination_type': 'class',
                                'relationship': 'extends',
                                'file_path': str(java_file),
                                'application': app_name,
                                'metadata': json.dumps({'inheritance_type': 'extends'})
                            })
                        
                        # Extract global variables (fields) with metadata
                        global_vars = extract_global_variables_with_metadata(node, source_code, class_name)
                        for var_info in global_vars:
                            if var_info['name'] and is_meaningful_variable(var_info['name']):
                                # Create standardized variable name
                                standardized_var = f"{class_name}.field.{var_info['name']}"
                                
                                # Add variable metadata
                                metadata.add_variable_metadata(
                                    standardized_var, class_name, 'field', str(java_file), app_name,
                                    original_name=var_info['name'],
                                    data_type=var_info['data_type'],
                                    access_modifier=var_info['access_modifier'],
                                    is_static=var_info['is_static'],
                                    is_final=var_info['is_final'],
                                    annotations=var_info['annotations'],
                                    storage_type=var_info['storage_type'],
                                    definition_location=f"{class_name} class field",
                                    scope='global'
                                )
                                
                                # Register variable
                                variable_registry[standardized_var] = {
                                    'original_name': var_info['name'],
                                    'class': class_name,
                                    'method': 'field',
                                    'file_path': str(java_file),
                                    'application': app_name
                                }
                                
                                # CLASS → VARIABLE relationship with metadata
                                ast_relationships.append({
                                    'source_node': class_name,
                                    'source_type': 'class',
                                    'destination_node': standardized_var,
                                    'destination_type': 'variable',
                                    'relationship': 'declares_variable',
                                    'file_path': str(java_file),
                                    'application': app_name,
                                    'metadata': json.dumps(var_info)
                                })
                
                elif node.type == 'method_declaration' and current_class:
                    method_info = extract_method_info_with_metadata(node, source_code)
                    method_name = method_info['method_name']
                    
                    if method_name:
                        # Register method with metadata
                        method_key = f"{current_class}.{method_name}"
                        metadata.add_method_metadata(
                            method_key, current_class, str(java_file), app_name,
                            return_type=method_info['return_type'],
                            parameters=method_info['parameters'],
                            annotations=method_info['annotations'],
                            access_modifier=method_info['access_modifier'],
                            is_static=method_info['is_static'],
                            is_abstract=method_info['is_abstract'],
                            is_final=method_info['is_final'],
                            throws_exceptions=method_info['throws_exceptions']
                        )
                        
                        method_registry[method_key] = {
                            'class': current_class,
                            'file_path': str(java_file),
                            'application': app_name,
                            'parameters': [p['name'] for p in method_info['parameters']]
                        }
                        
                        # CLASS → METHOD relationship with metadata
                        ast_relationships.append({
                            'source_node': current_class,
                            'source_type': 'class',
                            'destination_node': method_name,
                            'destination_type': 'method',
                            'relationship': 'declares',
                            'file_path': str(java_file),
                            'application': app_name,
                            'metadata': json.dumps(method_info)
                        })
                
                # Continue traversing
                for child in node.children:
                    traverse_ast_with_metadata(child, depth + 1)
            
            traverse_ast_with_metadata(tree.root_node)

all_relationships.extend(ast_relationships)
print(f'✅ AST analysis complete. Added {len(ast_relationships)} relationships with metadata')
print(f'📊 Total relationships: {len(all_relationships)}')
print(f'📊 Registered: {len(class_registry)} classes, {len(method_registry)} methods, {len(variable_registry)} global variables')

# ========== STAGE 4: GLOBAL VARIABLE TRACKING WITH TRANSFORMATION METADATA ==========

def analyze_variable_transformations_with_metadata():
    '''Analyze global variable transformations with comprehensive metadata'''
    print('🔗 Analyzing global variable transformations with metadata...')
    
    transformation_relationships = []
    
    # Analyze transformations for each application
    for app_name in APPLICATIONS.keys():
        app_path = BASE_PATH / app_name
        if not app_path.exists():
            continue
        
        java_files = list(app_path.rglob('*.java'))
        
        for java_file in java_files:
            try:
                with open(java_file, 'r', encoding='utf-8') as f:
                    source_code = f.read()
            except Exception:
                continue
            
            file_name = Path(java_file).stem
            
            # Find global variable usage patterns
            global_var_patterns = [
                # Service injection patterns
                r'@Autowired\s+(?:private|protected|public)?\s*(\w+)\s+(\w+)',
                r'@Inject\s+(?:private|protected|public)?\s*(\w+)\s+(\w+)',
                # Field declarations
                r'(?:private|protected|public)\s+(?:static\s+)?(?:final\s+)?(\w+)\s+(\w+)\s*[;=]',
                # Repository/Service usage
                r'(\w+)\s*=\s*(\w+Repository|\w+Service)\.',
                r'(\w+)\s*=\s*(\w+)\.(save|find|get|create|update|delete)\(',
            ]
            
            transformations_found = []
            
            for pattern in global_var_patterns:
                matches = re.findall(pattern, source_code, re.MULTILINE | re.IGNORECASE)
                for match in matches:
                    if len(match) >= 2:
                        if isinstance(match, tuple):
                            var_type = match[0] if len(match) > 1 else 'Object'
                            var_name = match[1] if len(match) > 1 else match[0]
                        else:
                            var_type = 'Object'
                            var_name = match
                        
                        if is_meaningful_variable(var_name) and is_global_variable(var_name, source_code):
                            transformations_found.append({
                                'variable': var_name,
                                'type': var_type,
                                'pattern': pattern,
                                'context': 'global_field'
                            })
            
            # Create transformation relationships with metadata
            for i, transform in enumerate(transformations_found):
                var_name = transform['variable']
                var_type = transform['type']
                
                # Find the class this variable belongs to
                class_name = file_name  # Default to file name
                for registered_class in class_registry.keys():
                    if class_registry[registered_class]['file_path'] == str(java_file):
                        class_name = registered_class
                        break
                
                # Create standardized variable name
                standardized_var = f"{class_name}.field.{var_name}"
                
                # Update variable metadata with transformation info
                if standardized_var in metadata.variable_metadata:
                    current_transformations = metadata.variable_metadata[standardized_var].get('transformations', [])
                    current_transformations.append({
                        'type': 'field_usage',
                        'pattern': transform['pattern'],
                        'context': transform['context'],
                        'data_type': var_type,
                        'file': str(java_file)
                    })
                    metadata.variable_metadata[standardized_var]['transformations'] = current_transformations
                    metadata.variable_metadata[standardized_var]['usage_count'] += 1
                else:
                    # Add new variable metadata
                    metadata.add_variable_metadata(
                        standardized_var, class_name, 'field', str(java_file), app_name,
                        original_name=var_name,
                        data_type=var_type,
                        storage_type='field',
                        definition_location=f"{class_name} class",
                        scope='global',
                        transformations=[{
                            'type': 'field_usage',
                            'pattern': transform['pattern'],
                            'context': transform['context'],
                            'data_type': var_type,
                            'file': str(java_file)
                        }],
                        usage_count=1
                    )
                
                # Create transformation relationships between similar variables
                if i > 0:
                    prev_transform = transformations_found[i-1]
                    prev_standardized = f"{class_name}.field.{prev_transform['variable']}"
                    
                    transformation_metadata = {
                        'transformation_type': 'global_variable_flow',
                        'source_pattern': prev_transform['pattern'],
                        'target_pattern': transform['pattern'],
                        'data_flow': f"{prev_transform['type']} -> {var_type}",
                        'context': 'global_scope'
                    }
                    
                    transformation_relationships.append({
                        'source_node': prev_standardized,
                        'source_type': 'variable',
                        'destination_node': standardized_var,
                        'destination_type': 'variable',
                        'relationship': 'transforms_to',
                        'file_path': str(java_file),
                        'application': app_name,
                        'metadata': json.dumps(transformation_metadata)
                    })
    
    print(f'✅ Created {len(transformation_relationships)} global variable transformations with metadata')
    return transformation_relationships

def create_cross_class_variable_connections_with_metadata():
    '''Create cross-class variable connections with metadata'''
    print('🔗 Creating cross-class variable connections with metadata...')
    
    cross_connections = []
    
    # Group variables by original name across classes
    original_name_groups = defaultdict(list)
    for var_key, var_info in variable_registry.items():
        original_name = var_info['original_name']
        original_name_groups[original_name].append((var_key, var_info))
    
    # Create connections between classes that use the same variable names
    for original_name, var_list in original_name_groups.items():
        if len(var_list) > 1:
            # Create data flow connections with metadata
            for i in range(len(var_list) - 1):
                source_var, source_info = var_list[i]
                target_var, target_info = var_list[i + 1]
                
                if source_info['class'] != target_info['class']:
                    # Create rich metadata for cross-class connection
                    connection_metadata = {
                        'connection_type': 'cross_class_variable_usage',
                        'original_variable_name': original_name,
                        'source_class': source_info['class'],
                        'target_class': target_info['class'],
                        'source_application': source_info['application'],
                        'target_application': target_info['application'],
                        'is_cross_application': source_info['application'] != target_info['application'],
                        'data_flow_direction': f"{source_info['class']} -> {target_info['class']}"
                    }
                    
                    cross_connections.append({
                        'source_node': source_var,
                        'source_type': 'variable',
                        'destination_node': target_var,
                        'destination_type': 'variable',
                        'relationship': 'data_find',
                        'file_path': source_info['file_path'],
                        'application': source_info['application'],
                        'metadata': json.dumps(connection_metadata)
                    })
    
    print(f'✅ Created {len(cross_connections)} cross-class variable connections with metadata')
    return cross_connections

# Execute global variable tracking
var_transformations = analyze_variable_transformations_with_metadata()
cross_class_connections = create_cross_class_variable_connections_with_metadata()

# Add to master list
all_relationships.extend(var_transformations)
all_relationships.extend(cross_class_connections)

print(f'✅ Global variable tracking complete with rich metadata')
print(f'📊 Total relationships: {len(all_relationships)}')
print(f'📊 Global variables tracked: {len([v for v in variable_registry.keys() if ".field." in v])}')

# ========== STAGE 4B: METADATA-ENHANCED LLM PREPARATION ==========

def build_metadata_enhanced_system_prompt(file_path, current_relationships, metadata_collector):
    '''Build system prompt with comprehensive metadata context for LLM'''
    
    # Filter relationships for this specific file
    file_relationships = [r for r in current_relationships if r.get('file_path') == file_path]
    
    # Build AST context from current relationships
    ast_context = 'CURRENT AST RELATIONSHIPS:\n'
    for rel in file_relationships[:15]:  # Limit to avoid token overflow
        metadata_info = ''
        if rel.get('metadata'):
            try:
                meta = json.loads(rel['metadata'])
                if 'parent_class' in meta and meta['parent_class']:
                    metadata_info = f" (extends: {meta['parent_class']})"
                elif 'data_type' in meta and meta['data_type']:
                    metadata_info = f" (type: {meta['data_type']})"
                elif 'return_type' in meta and meta['return_type']:
                    metadata_info = f" (returns: {meta['return_type']})"
            except:
                pass
        
        ast_context += f"{rel['source_type']}:{rel['source_node']} -[{rel['relationship']}]-> {rel['destination_type']}:{rel['destination_node']}{metadata_info}\n"
    
    # Build class metadata context
    class_context = 'KNOWN CLASSES WITH METADATA:\n'
    for class_name, class_meta in metadata_collector.class_metadata.items():
        inheritance_info = ''
        if class_meta.get('parent_class'):
            inheritance_info = f" extends {class_meta['parent_class']}"
        if class_meta.get('interfaces'):
            inheritance_info += f" implements {', '.join(class_meta['interfaces'])}"
        
        annotations_info = ''
        if class_meta.get('annotations'):
            annotations_info = f" @{', @'.join([a.replace('@', '') for a in class_meta['annotations']])}"
        
        class_context += f'- {class_name} (app: {class_meta["application"]}){inheritance_info}{annotations_info}\n'
    
    # Build method metadata context
    method_context = 'KNOWN METHODS WITH METADATA:\n'
    for method_key, method_meta in list(metadata_collector.method_metadata.items())[:10]:  # Limit for token management
        params_info = ''
        if method_meta.get('parameters'):
            param_strs = [f"{p.get('type', 'Object')} {p.get('name', 'param')}" for p in method_meta['parameters']]
            params_info = f"({', '.join(param_strs)})"
        
        return_info = ''
        if method_meta.get('return_type'):
            return_info = f" -> {method_meta['return_type']}"
        
        annotations_info = ''
        if method_meta.get('annotations'):
            annotations_info = f" @{', @'.join([a.replace('@', '') for a in method_meta['annotations']])}"
        
        method_context += f'- {method_key}{params_info}{return_info}{annotations_info}\n'
    
    # Build global variable metadata context
    variable_context = 'GLOBAL VARIABLES WITH METADATA:\n'
    global_vars = {k: v for k, v in metadata_collector.variable_metadata.items() if v.get('scope') == 'global'}
    for var_key, var_meta in list(global_vars.items())[:10]:  # Limit for token management
        type_info = ''
        if var_meta.get('data_type'):
            type_info = f" ({var_meta['data_type']})"
        
        annotations_info = ''
        if var_meta.get('annotations'):
            annotations_info = f" @{', @'.join([a.replace('@', '') for a in var_meta['annotations']])}"
        
        transformations_info = ''
        if var_meta.get('transformations'):
            transform_count = len(var_meta['transformations'])
            transformations_info = f" [{transform_count} transformations]"
        
        variable_context += f'- {var_key}{type_info}{annotations_info}{transformations_info}\n'
    
    prompt = f"""
You are a Java code lineage extraction engine with RICH METADATA CONTEXT. Extract relationships using this comprehensive context:

METADATA-ENHANCED CONTEXT:
{class_context}

{method_context}

{variable_context}

{ast_context}

ENHANCED EXTRACTION RULES WITH METADATA:
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. PRIORITIZE relationships based on metadata:
   - class -[extends]-> class (use inheritance metadata)
   - class -[declares]-> method (use method metadata)
   - class -[declares_variable]-> variable (use field metadata)
   - method -[uses]-> variable (focus on global variables only)
   - variable -[transforms_to]-> variable (use transformation metadata)
   - class -[exposes]-> endpoint (extract from annotations like @RequestMapping)
   - class -[data_find]-> data (extract from JPA annotations like @Entity, @Table)
3. Extract REST API endpoints from Spring annotations (@GetMapping, @PostMapping, etc.)
4. Extract database entities from JPA annotations (@Entity, @Table, @Repository)
5. Focus on GLOBAL variables only (class fields, injected services)
6. Use metadata to determine relationship strength and importance
7. Standardize variable format: ClassName.field.variableName (global only)
8. Leverage inheritance metadata for better class relationships
9. Use annotation metadata for API and data layer detection

Extract triples in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the triples, no explanations.
"""
    return prompt

# Prepare documents for metadata-enhanced LLM processing
print('📄 Preparing documents for metadata-enhanced LLM processing...')

from langchain.text_splitter import Language as LC_Language

splitter = RecursiveCharacterTextSplitter.from_language(
    language=LC_Language.JAVA,
    chunk_size=3500,  # Slightly smaller to accommodate metadata
    chunk_overlap=200
)

java_docs, split_docs = [], []

for app_name in APPLICATIONS.keys():
    app_path = BASE_PATH / app_name
    if app_path.exists():
        for java_file in app_path.rglob('*.java'):
            try:
                loader = TextLoader(str(java_file), encoding='utf-8')
                docs = loader.load()
                java_docs.extend(docs)
            except Exception as e:
                continue

for doc in java_docs:
    split_docs.extend(splitter.split_documents([doc]))

print(f'📄 Prepared {len(split_docs)} document chunks for metadata-enhanced LLM processing')
print(f'📊 Metadata available: {len(metadata.class_metadata)} classes, {len(metadata.method_metadata)} methods, {len(metadata.variable_metadata)} variables')

# ========== STAGE 4C: METADATA-DRIVEN LLM PROCESSING ==========

def normalize_entity_with_metadata(entity_name, entity_type, metadata_collector):
    '''Normalize entity names using metadata for consistency'''
    if not entity_name:
        return entity_name
    
    # Remove prefixes
    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
    for prefix in prefixes:
        if entity_name.lower().startswith(prefix):
            entity_name = entity_name[len(prefix):]
    
    # Remove file extensions
    entity_name = re.sub(r'\.(java|class)$', '', entity_name, flags=re.IGNORECASE)
    
    # Use metadata for consistency
    if entity_type == 'class':
        # Match with class metadata for exact naming
        for class_name in metadata_collector.class_metadata.keys():
            if entity_name.lower() == class_name.lower():
                return class_name
    
    elif entity_type == 'method':
        # Match with method metadata
        for method_key in metadata_collector.method_metadata.keys():
            method_name = method_key.split('.')[-1]
            if entity_name.lower() == method_name.lower():
                return method_name
    
    elif entity_type == 'variable':
        # For variables, check if it should be standardized
        if '.' not in entity_name:
            # Look for matching variable in metadata
            for var_key, var_meta in metadata_collector.variable_metadata.items():
                if var_meta.get('original_name', '').lower() == entity_name.lower():
                    return var_key  # Return standardized name
    
    return entity_name

def extract_api_endpoints_from_metadata(chunk_content, class_name, metadata_collector):
    '''Extract API endpoints using metadata context'''
    endpoints = []
    
    # Check class metadata for controller annotations
    class_meta = metadata_collector.class_metadata.get(class_name, {})
    is_controller = any('@Controller' in str(ann) or '@RestController' in str(ann) 
                       for ann in class_meta.get('annotations', []))
    
    if is_controller:
        # Extract base path from class-level RequestMapping
        base_path = ''
        for ann in class_meta.get('annotations', []):
            if '@RequestMapping' in str(ann):
                path_match = re.search(r'["\']([^"\']*/[^"\']*)["\'']', str(ann))
                if path_match:
                    base_path = path_match.group(1)
        
        # Extract method-level mappings
        mapping_patterns = [
            r'@GetMapping\(["\']([^"\']*)["\'']\)',
            r'@PostMapping\(["\']([^"\']*)["\'']\)',
            r'@PutMapping\(["\']([^"\']*)["\'']\)',
            r'@DeleteMapping\(["\']([^"\']*)["\'']\)',
            r'@RequestMapping\([^)]*value\s*=\s*["\']([^"\']*)["\''][^)]*method\s*=\s*RequestMethod\.(\w+)',
        ]
        
        for pattern in mapping_patterns:
            matches = re.findall(pattern, chunk_content)
            for match in matches:
                if isinstance(match, tuple):
                    path = match[0]
                    method = match[1] if len(match) > 1 else 'GET'
                else:
                    path = match
                    method = 'GET'  # Default
                
                full_path = base_path + path if not path.startswith('/') else path
                endpoint = f"{method.upper()} {full_path}"
                endpoints.append(endpoint)
    
    return endpoints

def extract_data_entities_from_metadata(chunk_content, class_name, metadata_collector):
    '''Extract data entities using metadata context'''
    entities = []
    
    # Check class metadata for JPA annotations
    class_meta = metadata_collector.class_metadata.get(class_name, {})
    annotations = class_meta.get('annotations', [])
    
    for ann in annotations:
        ann_str = str(ann)
        if '@Entity' in ann_str:
            entities.append(f"entity_{class_name}")
        elif '@Table' in ann_str:
            # Extract table name
            table_match = re.search(r'name\s*=\s*["\']([^"\']*)["\'']', ann_str)
            if table_match:
                entities.append(f"table_{table_match.group(1)}")
            else:
                entities.append(f"table_{class_name.lower()}")
        elif '@Repository' in ann_str:
            entities.append(f"repository_{class_name}")
    
    return entities

# Initialize metadata-enhanced LLM lineage collection
all_metadata_llm_lineage = []

print('🤖 Starting metadata-driven LLM extraction...')

for chunk in tqdm(split_docs, desc='Metadata-Enhanced LLM Processing'):
    file_path = chunk.metadata.get('source')
    
    # Build metadata-enhanced system prompt
    system_prompt = build_metadata_enhanced_system_prompt(file_path, all_relationships, metadata)
    
    # Extract class name from file path for context
    file_name = os.path.basename(file_path) if file_path else 'unknown'
    class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name
    
    # Determine application
    app_name = 'ServiceBolt' if 'ServiceBolt' in str(file_path) else 'UnifiedBolt'
    
    # Extract API endpoints using metadata
    api_endpoints = extract_api_endpoints_from_metadata(chunk.page_content, class_name, metadata)
    for endpoint in api_endpoints:
        all_metadata_llm_lineage.append({
            'source_node': class_name,
            'source_type': 'class',
            'destination_node': endpoint,
            'destination_type': 'endpoint',
            'relationship': 'exposes',
            'file_path': file_path,
            'application': app_name,
            'metadata': json.dumps({
                'endpoint_type': 'REST_API',
                'extracted_from': 'metadata_annotations',
                'class_metadata': metadata.class_metadata.get(class_name, {})
            })
        })
    
    # Extract data entities using metadata
    data_entities = extract_data_entities_from_metadata(chunk.page_content, class_name, metadata)
    for entity in data_entities:
        all_metadata_llm_lineage.append({
            'source_node': class_name,
            'source_type': 'class',
            'destination_node': entity,
            'destination_type': 'data',
            'relationship': 'data_find',
            'file_path': file_path,
            'application': app_name,
            'metadata': json.dumps({
                'entity_type': 'JPA_ENTITY',
                'extracted_from': 'metadata_annotations',
                'class_metadata': metadata.class_metadata.get(class_name, {})
            })
        })
    
    # LLM Graph Transformer with metadata-enhanced context
    transformer = LLMGraphTransformer(
        llm=llm,
        additional_instructions=system_prompt,
        allowed_nodes=['project', 'application', 'folder', 'file', 'class', 'method', 'interface', 'variable', 'endpoint', 'data'],
        allowed_relationships=[
            ('project', 'contains', 'application'),
            ('application', 'contains', 'folder'),
            ('folder', 'contains', 'folder'),
            ('folder', 'contains', 'file'),
            ('file', 'contains', 'class'),
            ('file', 'declares', 'interface'),
            ('class', 'declares', 'method'),
            ('class', 'declares_variable', 'variable'),
            ('class', 'exposes', 'endpoint'),
            ('class', 'extends', 'class'),
            ('class', 'implements', 'interface'),
            ('class', 'has_field', 'variable'),
            ('method', 'uses', 'variable'),
            ('variable', 'transforms_to', 'variable'),
            ('class', 'data_find', 'data'),
            ('method', 'data_find', 'data'),
        ],
        strict_mode=True,
        node_properties=False,
        relationship_properties=False,
    )
    
    try:
        graph_docs = transformer.convert_to_graph_documents([chunk])
        for gd in graph_docs:
            for rel in gd.relationships:
                s_node = rel.source.id.strip()
                s_type = rel.source.type.strip().lower()
                t_node = rel.target.id.strip()
                t_type = rel.target.type.strip().lower()
                rel_type = rel.type.strip().lower()

                # Normalize entities using metadata
                s_node = normalize_entity_with_metadata(s_node, s_type, metadata)
                t_node = normalize_entity_with_metadata(t_node, t_type, metadata)
                
                # Skip if empty or invalid
                if not s_node or not t_node or s_node == t_node:
                    continue
                
                # Standardize variable names if they're variables (global only)
                if s_type == 'variable' and '.' not in s_node:
                    # Check if it's a global variable using metadata
                    is_global = any(s_node in var_meta.get('original_name', '') 
                                  for var_meta in metadata.variable_metadata.values() 
                                  if var_meta.get('scope') == 'global')
                    if is_global:
                        s_node = f"{class_name}.field.{s_node}"
                    else:
                        continue  # Skip local variables
                
                if t_type == 'variable' and '.' not in t_node:
                    # Check if it's a global variable using metadata
                    is_global = any(t_node in var_meta.get('original_name', '') 
                                  for var_meta in metadata.variable_metadata.values() 
                                  if var_meta.get('scope') == 'global')
                    if is_global:
                        t_node = f"{class_name}.field.{t_node}"
                    else:
                        continue  # Skip local variables
                
                # Create metadata for this relationship
                relationship_metadata = {
                    'extracted_by': 'metadata_enhanced_llm',
                    'source_metadata': metadata.class_metadata.get(s_node, {}) if s_type == 'class' else {},
                    'target_metadata': metadata.class_metadata.get(t_node, {}) if t_type == 'class' else {},
                    'confidence': 'high'  # Metadata-enhanced extraction has higher confidence
                }
                
                all_metadata_llm_lineage.append({
                    'source_node': s_node,
                    'source_type': s_type,
                    'destination_node': t_node,
                    'destination_type': t_type,
                    'relationship': rel_type,
                    'file_path': file_path,
                    'application': app_name,
                    'metadata': json.dumps(relationship_metadata)
                })
    except Exception as e:
        print(f'⚠️ Metadata-enhanced LLM processing error for {file_path}: {e}')
        continue

# Add metadata-enhanced LLM relationships to master list
all_relationships.extend(all_metadata_llm_lineage)

print(f'✅ Metadata-driven LLM processing complete. Added {len(all_metadata_llm_lineage)} relationships')
print(f'📊 Total relationships: {len(all_relationships)}')

if len(all_metadata_llm_lineage) > 0:
    llm_df = pd.DataFrame(all_metadata_llm_lineage)
    print(f'   🔗 Metadata-enhanced LLM relationship types: {llm_df["relationship"].value_counts().to_dict()}')
    print(f'   🏢 Metadata-enhanced LLM applications: {llm_df["application"].value_counts().to_dict()}')
    
    # Show API endpoints and data entities extracted
    endpoints = llm_df[llm_df['destination_type'] == 'endpoint']['destination_node'].tolist()
    data_entities = llm_df[llm_df['destination_type'] == 'data']['destination_node'].tolist()
    print(f'   🌐 API endpoints extracted: {len(endpoints)}')
    print(f'   🗄️ Data entities extracted: {len(data_entities)}')

# ========== STAGE 5: DATA CLEANING AND METADATA VALIDATION ==========

def clean_and_validate_data_with_metadata():
    '''Clean, validate, and prepare data with metadata for Neo4j'''
    print('🧹 Cleaning and validating data with metadata...')
    
    # Convert to DataFrame for easier processing
    df = pd.DataFrame(all_relationships)
    
    print(f'📊 Initial dataset: {len(df)} relationships')
    
    # 1. Handle missing values
    df['file_path'] = df['file_path'].fillna('')
    df['application'] = df['application'].fillna('Unknown')
    df['metadata'] = df['metadata'].fillna('{}')
    
    # 2. Remove duplicates
    initial_count = len(df)
    df = df.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])
    print(f'🔄 Removed {initial_count - len(df)} duplicates')
    
    # 3. Validate node names
    def is_valid_node_name(name):
        if pd.isna(name) or str(name).strip() == '':
            return False
        return True
    
    valid_mask = (df['source_node'].apply(is_valid_node_name) & 
                  df['destination_node'].apply(is_valid_node_name))
    
    invalid_count = len(df) - valid_mask.sum()
    if invalid_count > 0:
        print(f'🔄 Removed {invalid_count} relationships with invalid node names')
        df = df[valid_mask]
    
    # 4. Validate metadata JSON
    def is_valid_metadata(meta_str):
        try:
            json.loads(str(meta_str))
            return True
        except:
            return False
    
    # Fix invalid metadata
    invalid_metadata_mask = ~df['metadata'].apply(is_valid_metadata)
    df.loc[invalid_metadata_mask, 'metadata'] = '{}'
    print(f'🔄 Fixed {invalid_metadata_mask.sum()} invalid metadata entries')
    
    # 5. Standardize relationship types
    relationship_mapping = {
        'contains': 'CONTAINS',
        'declares': 'DECLARES',
        'declares_variable': 'DECLARES_VARIABLE',
        'extends': 'EXTENDS',
        'implements': 'IMPLEMENTS',
        'uses': 'USES',
        'transforms_to': 'TRANSFORMS_TO',
        'data_find': 'DATA_FIND',
        'exposes': 'EXPOSES',
        'has_field': 'HAS_FIELD'
    }
    
    df['neo4j_relationship'] = df['relationship'].map(relationship_mapping)
    df['neo4j_relationship'] = df['neo4j_relationship'].fillna(df['relationship'].str.upper())
    
    # 6. Add node labels for Neo4j
    node_type_mapping = {
        'project': 'Project',
        'application': 'Application',
        'folder': 'Folder',
        'file': 'File',
        'class': 'Class',
        'method': 'Method',
        'interface': 'Interface',
        'variable': 'Variable',
        'endpoint': 'Endpoint',
        'data': 'Data'
    }
    
    df['source_label'] = df['source_type'].map(node_type_mapping)
    df['destination_label'] = df['destination_type'].map(node_type_mapping)
    
    # 7. Validate global variables only
    global_vars = df[df['destination_type'] == 'variable']
    properly_standardized = global_vars['destination_node'].str.contains(r'^[^.]+\.field\.[^.]+$', na=False)
    non_global_vars = global_vars[~properly_standardized]
    
    if len(non_global_vars) > 0:
        print(f'🔄 Removing {len(non_global_vars)} non-global variables')
        df = df[~df.index.isin(non_global_vars.index)]
    
    # 8. Generate comprehensive statistics
    print('\n📊 Final Dataset Statistics:')
    print(f'   Total relationships: {len(df)}')
    print(f'   Relationship types: {df["relationship"].value_counts().to_dict()}')
    print(f'   Applications: {df["application"].value_counts().to_dict()}')
    print(f'   Node types: {df["source_type"].value_counts().to_dict()}')
    
    # 9. Validate metadata richness
    metadata_with_content = df[df['metadata'] != '{}']['metadata'].count()
    print(f'   Relationships with metadata: {metadata_with_content}/{len(df)} ({metadata_with_content/len(df)*100:.1f}%)')
    
    # 10. Validate global variables
    global_vars_final = df[df['destination_type'] == 'variable']['destination_node']
    properly_standardized_final = global_vars_final.str.contains(r'^[^.]+\.field\.[^.]+$', na=False)
    print(f'   Global variables (properly standardized): {properly_standardized_final.sum()}/{len(global_vars_final)}')
    
    # 11. API endpoints and data entities
    endpoints_count = len(df[df['destination_type'] == 'endpoint'])
    data_entities_count = len(df[df['destination_type'] == 'data'])
    print(f'   API endpoints: {endpoints_count}')
    print(f'   Data entities: {data_entities_count}')
    
    # 12. Export cleaned data with metadata
    df.to_csv('oneinsights_v12_metadata_enhanced_relationships.csv', index=False)
    print(f'✅ Exported metadata-enhanced dataset to oneinsights_v12_metadata_enhanced_relationships.csv')
    
    # 13. Export metadata summaries
    metadata_summary = {
        'class_metadata': metadata.class_metadata,
        'method_metadata': metadata.method_metadata,
        'variable_metadata': metadata.variable_metadata,
        'file_metadata': metadata.file_metadata,
        'folder_metadata': metadata.folder_metadata,
        'app_metadata': metadata.app_metadata
    }
    
    with open('oneinsights_v12_metadata_summary.json', 'w') as f:
        json.dump(metadata_summary, f, indent=2, default=str)
    print(f'✅ Exported metadata summary to oneinsights_v12_metadata_summary.json')
    
    return df

# Clean the data with metadata
df_clean_metadata = clean_and_validate_data_with_metadata()

# ========== STAGE 6: NEO4J LOADING WITH METADATA PROPERTIES ==========

def load_to_neo4j_with_metadata(df):
    '''Load clean data to Neo4j with metadata properties'''
    if graph is None:
        print('❌ Neo4j connection not available')
        return
    
    try:
        print('🚀 Loading data to Neo4j with metadata properties...')
        
        # Clear existing data
        print('🧹 Clearing existing data...')
        graph.query('MATCH (n) DETACH DELETE n')
        
        # Load data in batches with metadata
        batch_size = 50  # Smaller batches due to metadata
        total_batches = len(df) // batch_size + (1 if len(df) % batch_size > 0 else 0)
        
        print(f'📊 Loading {len(df)} relationships in {total_batches} batches with metadata...')
        
        for i in tqdm(range(0, len(df), batch_size), desc='Loading to Neo4j with Metadata', total=total_batches):
            batch = df.iloc[i:i+batch_size]
            
            for _, row in batch.iterrows():
                try:
                    # Parse metadata
                    metadata_dict = {}
                    try:
                        metadata_dict = json.loads(row['metadata'])
                    except:
                        metadata_dict = {}
                    
                    # Create enhanced Cypher query with metadata
                    source_props = {
                        'name': str(row['source_node']),
                        'application': str(row['application']),
                        'node_type': str(row['source_type'])
                    }
                    
                    target_props = {
                        'name': str(row['destination_node']),
                        'application': str(row['application']),
                        'node_type': str(row['destination_type'])
                    }
                    
                    # Add metadata properties based on node type
                    if row['source_type'] == 'class' and row['source_node'] in metadata.class_metadata:
                        class_meta = metadata.class_metadata[row['source_node']]
                        source_props.update({
                            'parent_class': class_meta.get('parent_class', ''),
                            'package': class_meta.get('package', ''),
                            'is_abstract': class_meta.get('is_abstract', False),
                            'access_modifier': class_meta.get('access_modifier', 'public')
                        })
                    
                    if row['destination_type'] == 'variable' and row['destination_node'] in metadata.variable_metadata:
                        var_meta = metadata.variable_metadata[row['destination_node']]
                        target_props.update({
                            'data_type': var_meta.get('data_type', ''),
                            'scope': var_meta.get('scope', 'global'),
                            'storage_type': var_meta.get('storage_type', 'field'),
                            'usage_count': var_meta.get('usage_count', 0)
                        })
                    
                    # Create Cypher query with properties
                    cypher = f"""
                    MERGE (source:{row['source_label']} $source_props)
                    MERGE (target:{row['destination_label']} $target_props)
                    MERGE (source)-[:{row['neo4j_relationship']} {{metadata: $metadata}}]->(target)
                    """
                    
                    # Execute query
                    graph.query(cypher, {
                        'source_props': source_props,
                        'target_props': target_props,
                        'metadata': json.dumps(metadata_dict)
                    })
                
                except Exception as e:
                    continue
        
        # Create indexes for better performance
        print('🔧 Creating indexes...')
        indexes = [
            'CREATE INDEX IF NOT EXISTS FOR (n:Project) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Variable) ON (n.scope)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Endpoint) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Data) ON (n.name)'
        ]
        
        for index in indexes:
            try:
                graph.query(index)
            except:
                pass
        
        print('✅ Metadata-enhanced data successfully loaded to Neo4j!')
        
        # Verify the load with metadata
        verify_neo4j_load_with_metadata()
        
    except Exception as e:
        print(f'❌ Error loading to Neo4j: {e}')

def verify_neo4j_load_with_metadata():
    '''Verify the Neo4j load and show metadata-enhanced statistics'''
    print('\n🔍 Verifying Neo4j load with metadata...')
    
    try:
        # Node statistics
        node_stats = graph.query("""
        MATCH (n)
        RETURN labels(n)[0] as node_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('📊 Node Statistics:')
        for stat in node_stats:
            print(f'   {stat["node_type"]}: {stat["count"]} nodes')
        
        # Project structure
        project_structure = graph.query("""
        MATCH (p:Project)-[:CONTAINS]->(a:Application)
        RETURN p.name as project, collect(a.name) as applications
        """)
        
        print('\n🏗️ Project Structure:')
        for struct in project_structure:
            print(f'   {struct["project"]} → {struct["applications"]}')
        
        # Global variables with metadata
        sample_vars = graph.query("""
        MATCH (v:Variable)
        WHERE v.scope = 'global' AND v.name CONTAINS '.field.'
        RETURN v.name as variable_name, v.data_type as data_type
        LIMIT 5
        """)
        
        print('\n🔗 Sample Global Variables with Metadata:')
        for var in sample_vars:
            print(f'   {var["variable_name"]} ({var["data_type"]})')
        
    except Exception as e:
        print(f'❌ Error verifying Neo4j load: {e}')

# Load to Neo4j with metadata
load_to_neo4j_with_metadata(df_clean_metadata)

print('\n🎉 ONEINSIGHTS V12 METADATA-ENHANCED ANALYSIS COMPLETE!')
print('\n✅ Achievements:')
print('   🏗️ Complete hierarchy with metadata')
print('   🔗 OneInsights project connects to BOTH applications')
print('   📊 Global variables standardized as ClassName.field.variableName')
print('   🤖 Metadata-enhanced LLM processing')
print('   🔍 AST + Metadata + LLM combined analysis')
print('   📋 Comprehensive metadata for all node types')
print('   🧹 Clean, validated data with metadata properties in Neo4j')
print('   📁 Complete CSV and JSON exports for analysis')
print('\n🎯 Ready for advanced metadata-driven graph analysis!')