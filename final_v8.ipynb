{"cells": [{"cell_type": "markdown", "id": "improvements_summary", "metadata": {}, "source": ["# 🚀 Focused Java Code + Data Lineage Analysis v9 - FILTERED VERSION\n", "\n", "## 🎯 Focused Node Types:\n", "\n", "### **Nodes Captured** ✅\n", "- **PROJECT** - Root project node\n", "- **APPLICATIONS** - ServiceBolt, UnifiedBolt applications\n", "- **FOLDERS** - Directory structure\n", "- **FILE** - Java source files\n", "- **CLASS** - Java classes\n", "- **METHOD** - Class methods\n", "- **INTERFACE** - Java interfaces (minimal)\n", "- **VARIABLE** - Meaningful variables only\n", "- **ENDPOINT** - REST API endpoints\n", "- **DATA** - Database tables and data entities\n", "\n", "### **Relationships Captured** ✅\n", "- **CONTAINS** - Hierarchical containment (project->app, folder->file, etc.)\n", "- **DATA_FIND** - Data discovery and retrieval operations\n", "- **DECLARES** - Declaration relationships (file->class, class->method)\n", "- **DECLARES_VARIABLE** - Variable declarations\n", "- **EXPOSES** - API endpoint exposure\n", "- **EXTENDS** - Class inheritance\n", "- **HAS_FIELD** - Field ownership\n", "- **IMPLEMENTS** - Interface implementation (minimal)\n", "- **TRANSFORMS_TO** - Data transformation flows\n", "- **USES** - Usage relationships\n", "\n", "## 🚫 Filtered Out:\n", "- Noise variables (i, j, temp, etc.)\n", "- Overly granular relationships\n", "- Complex variable flows that don't add business value\n", "- Redundant interface relationships\n", "\n", "## 🎯 Focus Areas:\n", "- **Global variable tracking** over local variables\n", "- **State changes** and data transformations\n", "- **Class extends relationships** prioritized over interfaces\n", "- **Business-meaningful connections** only"]}, {"cell_type": "code", "execution_count": 23, "id": "setup", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Neo4j connection established\n", "🚀 Setup complete! Ready for analysis...\n"]}], "source": ["# ========== IMPORTS AND SETUP ==========\n", "import os\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "import re\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# ========== CONFIGURATION ==========\n", "BASE_PATH = Path(r'C:/Shaik/sample/OneInsights')\n", "NEO4J_URI = 'bolt://localhost:7687'\n", "NEO4J_USER = 'neo4j'\n", "NEO4J_PASSWORD = 'Test@7889'\n", "NEO4J_DB = 'oneinsights-v8'\n", "GOOGLE_API_KEY = 'AIzaSyAeSuntl3dxGqQhwxRG_jom1V_EjxEPSwc'\n", "\n", "# ========== INITIALIZE COMPONENTS ==========\n", "try:\n", "    graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "    print('✅ Neo4j connection established')\n", "except Exception as e:\n", "    print(f'❌ Neo4j connection failed: {e}')\n", "    graph = None\n", "\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "llm = ChatGoogleGenerativeAI(\n", "    model='gemini-2.0-flash-exp',\n", "    temperature=0,\n", "    google_api_key=GOOGLE_API_KEY\n", ")\n", "\n", "# ========== ENHANCED FILTERING ==========\n", "NOISE_VARIABLES = {\n", "    # Loop variables\n", "    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',\n", "    # Common temporary variables\n", "    'temp', 'tmp', 'obj', 'item', 'elem', 'node', 'val', 'value',\n", "    # Common short variables\n", "    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',\n", "    # Reserved keywords\n", "    'this', 'super', 'null', 'true', 'false', 'void', 'return',\n", "    # Meaningless short names\n", "    'it', 'ex', 'e1', 'e2', 'o1', 'o2'\n", "}\n", "\n", "APPLICATIONS = {\n", "    'ServiceBolt': 'REST API Service Layer',\n", "    'UnifiedBolt': 'Core Business Logic and Data Layer'\n", "}\n", "\n", "def is_meaningful_variable(var_name):\n", "    '''Filter out noise variables and keep only meaningful ones'''\n", "    if not var_name or len(var_name) < 2:\n", "        return False\n", "    \n", "    if var_name.lower() in NOISE_VARIABLES:\n", "        return False\n", "    \n", "    if len(var_name) > 3:\n", "        return True\n", "    \n", "    meaningful_short = {'id', 'url', 'api', 'key', 'dto', 'dao', 'req', 'res', 'ctx'}\n", "    return var_name.lower() in meaningful_short\n", "\n", "def detect_application(file_path):\n", "    '''Detect which application a file belongs to'''\n", "    path_str = str(file_path).replace('\\\\', '/')\n", "    if 'ServiceBolt' in path_str:\n", "        return 'ServiceBolt'\n", "    elif 'UnifiedBolt' in path_str:\n", "        return 'UnifiedBolt'\n", "    return 'Unknown'\n", "\n", "print('🚀 Setup complete! Ready for analysis...')"]}, {"cell_type": "code", "execution_count": 24, "id": "folder_hierarchy", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📁 Extracting folder and file hierarchy...\n", "✅ Stage 1 Complete: 8 folder relationships, 19 file relationships, 2 applications\n"]}], "source": ["# ========== STAGE 1: FOLDER + FILE HIERARCHY ==========\n", "\n", "def extract_enhanced_folder_file_hierarchy(base_path):\n", "    '''Extract folder and file relationships with application context'''\n", "    folder_records, file_records, app_records = [], [], []\n", "    base_path = os.path.abspath(base_path)\n", "    base_folder_name = os.path.basename(base_path)\n", "    processed_folders = set()\n", "    detected_apps = set()\n", "\n", "    for root, dirs, files in os.walk(base_path):\n", "        rel_root = os.path.relpath(root, base_path)\n", "        parent_folder = base_folder_name if rel_root == '.' else os.path.dirname(rel_root) or base_folder_name\n", "        current_folder = base_folder_name if rel_root == '.' else os.path.basename(rel_root)\n", "        \n", "        app_name = detect_application(root)\n", "        if app_name != 'Unknown':\n", "            detected_apps.add(app_name)\n", "\n", "        folder_key = f'{parent_folder}->{current_folder}'\n", "        if folder_key not in processed_folders and parent_folder != current_folder:\n", "            folder_records.append({\n", "                'source_node': parent_folder,\n", "                'source_type': 'folder',\n", "                'destination_node': current_folder,\n", "                'destination_type': 'folder',\n", "                'relationship': 'contains',\n", "                'file_path': None,\n", "                'application': app_name\n", "            })\n", "            processed_folders.add(folder_key)\n", "\n", "        for f in files:\n", "            if f.endswith('.java'):\n", "                file_rel_path = os.path.relpath(os.path.join(root, f), base_path)\n", "                file_records.append({\n", "                    'source_node': current_folder,\n", "                    'source_type': 'folder',\n", "                    'destination_node': f,\n", "                    'destination_type': 'file',\n", "                    'relationship': 'contains',\n", "                    'file_path': file_rel_path,\n", "                    'application': app_name\n", "                })\n", "    \n", "    # Create application nodes\n", "    for app in detected_apps:\n", "        app_records.append({\n", "            'source_node': base_folder_name,\n", "            'source_type': 'project',\n", "            'destination_node': app,\n", "            'destination_type': 'application',\n", "            'relationship': 'contains',\n", "            'file_path': None,\n", "            'application': app\n", "        })\n", "    \n", "    return folder_records, file_records, app_records\n", "\n", "print('📁 Extracting folder and file hierarchy...')\n", "folder_records, file_records, app_records = extract_enhanced_folder_file_hierarchy(BASE_PATH)\n", "df_folders = pd.DataFrame(folder_records)\n", "df_files = pd.DataFrame(file_records)\n", "df_apps = pd.DataFrame(app_records)\n", "\n", "print(f'✅ Stage 1 Complete: {len(df_folders)} folder relationships, {len(df_files)} file relationships, {len(df_apps)} applications')"]}, {"cell_type": "code", "execution_count": 25, "id": "utility_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 Utility functions loaded\n"]}], "source": ["# ========== UTILITY FUNCTIONS ==========\n", "\n", "def read_source_code(file_path):\n", "    '''Read source code file'''\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        return f.read().encode('utf-8')\n", "\n", "def extract_package_and_imports(source_code_str):\n", "    '''Extract package name and imports from Java source'''\n", "    package_pattern = r'package\\s+([\\w\\.]+);'\n", "    import_pattern = r'import\\s+([\\w\\.]+);'\n", "    package_match = re.search(package_pattern, source_code_str)\n", "    package_name = package_match.group(1) if package_match else None\n", "    import_matches = re.findall(import_pattern, source_code_str)\n", "    return package_name, import_matches\n", "\n", "def extract_api_endpoints(source_code_str):\n", "    '''Extract REST API endpoints from Spring annotations'''\n", "    endpoints = []\n", "    mapping_patterns = {\n", "        'RequestMapping': [\n", "            r'@RequestMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@RequestMapping\\s*\\(\\s*path\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'GetMapping': [\n", "            r'@GetMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@GetMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'PostMapping': [\n", "            r'@PostMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "            r'@PostMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'PutMapping': [\n", "            r'@PutMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ],\n", "        'DeleteMapping': [\n", "            r'@DeleteMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "        ]\n", "    }\n", "    \n", "    for mapping_type, patterns in mapping_patterns.items():\n", "        for pattern in patterns:\n", "            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)\n", "            for match in matches:\n", "                if match.strip():\n", "                    endpoints.append({\n", "                        'type': mapping_type,\n", "                        'path': match.strip(),\n", "                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'\n", "                    })\n", "    \n", "    return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "    '''Extract database entities from JPA annotations'''\n", "    entities = []\n", "    \n", "    # Entity detection\n", "    entity_patterns = [\n", "        r'@Entity\\s*(?:\\([^)]*\\))?',\n", "        r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "    ]\n", "    \n", "    for pattern in entity_patterns:\n", "        if re.search(pattern, source_code_str, re.MULTILINE):\n", "            table_matches = re.findall(r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "            for table_name in table_matches:\n", "                if table_name.strip():\n", "                    entities.append({\n", "                        'type': 'table',\n", "                        'name': table_name.strip()\n", "                    })\n", "    \n", "    # Repository pattern detection\n", "    repository_pattern = r'interface\\s+(\\w+)\\s+extends\\s+.*Repository'\n", "    repo_matches = re.findall(repository_pattern, source_code_str)\n", "    for repo_name in repo_matches:\n", "        entity_name = repo_name.replace('Repository', '').replace('Rep', '')\n", "        if entity_name:\n", "            entities.append({\n", "                'type': 'table',\n", "                'name': entity_name.lower()\n", "            })\n", "    \n", "    return entities\n", "\n", "def extract_class_relationships(source_code_str):\n", "    '''Extract class inheritance relationships'''\n", "    relationships = []\n", "    \n", "    # Class extends\n", "    class_extends_pattern = r'class\\s+(\\w+)\\s+extends\\s+([\\w<>]+)'\n", "    class_matches = re.findall(class_extends_pattern, source_code_str)\n", "    for child_class, parent_class in class_matches:\n", "        parent_class = re.sub(r'<.*?>', '', parent_class).strip()\n", "        if parent_class:\n", "            relationships.append({\n", "                'child_class': child_class,\n", "                'parent_class': parent_class,\n", "                'relationship_type': 'extends'\n", "            })\n", "    \n", "    # Class implements (minimal as per user preference)\n", "    implements_pattern = r'class\\s+(\\w+)(?:\\s+extends\\s+\\w+)?\\s+implements\\s+([\\w<>,\\s]+)'\n", "    impl_matches = re.findall(implements_pattern, source_code_str)\n", "    for class_name, implements_clause in impl_matches:\n", "        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]\n", "        for interface in interfaces:\n", "            if interface and len(interface) > 3:\n", "                relationships.append({\n", "                    'child_class': class_name,\n", "                    'parent_class': interface,\n", "                    'relationship_type': 'implements'\n", "                })\n", "    \n", "    return relationships\n", "\n", "print('🔧 Utility functions loaded')"]}, {"cell_type": "code", "execution_count": 26, "id": "data_lineage", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Data lineage patterns loaded\n"]}], "source": ["# ========== DATA LINEAGE PATTERNS ==========\n", "\n", "def extract_data_lineage_patterns(source_code_str):\n", "    '''Extract data lineage patterns from Java code'''\n", "    lineage_records = []\n", "    \n", "    # 1. SQL Query patterns - Data Sources\n", "    sql_patterns = [\n", "        r'@Query\\s*\\(\\s*[\"\\']([^\"\\']*(SELECT|INSERT|UPDATE|DELETE)[^\"\\']*)[\"\\']',\n", "        r'@Query\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']*(SELECT|INSERT|UPDATE|DELETE)[^\"\\']*)[\"\\']',\n", "        r'@Query\\s*\\(\\s*nativeQuery\\s*=\\s*true\\s*,\\s*value\\s*=\\s*[\"\\']([^\"\\']*(SELECT|INSERT|UPDATE|DELETE)[^\"\\']*)[\"\\']'\n", "    ]\n", "    \n", "    for pattern in sql_patterns:\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE | re.IGNORECASE | re.DOTALL)\n", "        for match in matches:\n", "            if isinstance(match, tuple):\n", "                query = match[0]\n", "                operation = match[1].upper()\n", "            else:\n", "                query = match\n", "                operation = 'SELECT'\n", "            \n", "            # Extract table names from SQL\n", "            table_patterns = [\n", "                r'FROM\\s+(\\w+)',\n", "                r'JOIN\\s+(\\w+)',\n", "                r'UPDATE\\s+(\\w+)',\n", "                r'INSERT\\s+INTO\\s+(\\w+)',\n", "                r'DELETE\\s+FROM\\s+(\\w+)'\n", "            ]\n", "            \n", "            for table_pattern in table_patterns:\n", "                table_matches = re.findall(table_pattern, query, re.IGNORECASE)\n", "                for table_name in table_matches:\n", "                    lineage_records.append({\n", "                        'source_type': 'table',\n", "                        'source_name': table_name.lower(),\n", "                        'target_type': 'query',\n", "                        'target_name': f'{operation}_query',\n", "                        'operation': operation,\n", "                        'lineage_type': 'data_source'\n", "                    })\n", "    \n", "    # 2. Data Transformation patterns\n", "    transformation_patterns = [\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\.stream\\s*\\(\\s*\\)\\.map\\s*\\(',\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\.stream\\s*\\(\\s*\\)\\.filter\\s*\\(',\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\.stream\\s*\\(\\s*\\)\\.collect\\s*\\(',\n", "        r'(\\w+)\\s*=\\s*convert\\w*\\s*\\(\\s*(\\w+)',\n", "        r'(\\w+)\\s*=\\s*transform\\w*\\s*\\(\\s*(\\w+)',\n", "        r'(\\w+)\\s*=\\s*\\w*Mapper\\s*\\.\\s*\\w+\\s*\\(\\s*(\\w+)'\n", "    ]\n", "    \n", "    for pattern in transformation_patterns:\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            if len(match) >= 2:\n", "                target_var = match[0]\n", "                source_var = match[1]\n", "                \n", "                if is_meaningful_variable(target_var) and is_meaningful_variable(source_var):\n", "                    lineage_records.append({\n", "                        'source_type': 'data',\n", "                        'source_name': source_var,\n", "                        'target_type': 'data',\n", "                        'target_name': target_var,\n", "                        'operation': 'transform',\n", "                        'lineage_type': 'data_transformation'\n", "                    })\n", "    \n", "    # 3. API Data Flow patterns\n", "    api_patterns = [\n", "        r'(\\w+)\\s*=\\s*restTemplate\\s*\\.\\s*(get|post|put|delete)\\w*\\s*\\(',\n", "        r'(\\w+)\\s*=\\s*(\\w+Service)\\s*\\.\\s*(\\w+)\\s*\\(',\n", "        r'(\\w+)\\s*=\\s*(\\w+Repository)\\s*\\.\\s*(find|save|delete)\\w*\\s*\\('\n", "    ]\n", "    \n", "    for pattern in api_patterns:\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            if len(match) >= 2:\n", "                result_var = match[0]\n", "                service_name = match[1] if len(match) >= 2 else 'external_service'\n", "                operation = match[2] if len(match) >= 3 else 'call'\n", "                \n", "                if is_meaningful_variable(result_var):\n", "                    lineage_records.append({\n", "                        'source_type': 'service',\n", "                        'source_name': service_name,\n", "                        'target_type': 'data',\n", "                        'target_name': result_var,\n", "                        'operation': operation,\n", "                        'lineage_type': 'service_data_flow'\n", "                    })\n", "    \n", "    return lineage_records\n", "\n", "def build_data_lineage_graph(source_code_str, class_name, app_name):\n", "    '''Build comprehensive data lineage graph for a class'''\n", "    all_lineage = extract_data_lineage_patterns(source_code_str)\n", "    \n", "    # Add class and application context\n", "    for record in all_lineage:\n", "        record['class_name'] = class_name\n", "        record['application'] = app_name\n", "    \n", "    return all_lineage\n", "\n", "print('📊 Data lineage patterns loaded')"]}, {"cell_type": "code", "execution_count": 27, "id": "class_registry", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Building class registry...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files: 100%|██████████| 19/19 [00:00<00:00, 56.75it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Class registry built:\n", "   📁 19 classes processed\n", "   🌐 10 API endpoints found\n", "   🗄️ 3 database entities found\n", "   📊 8 data lineage patterns found\n", "   🔗 0 inter-application dependencies\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== CLASS REGISTRY WITH DATA LINEAGE ==========\n", "\n", "def build_enhanced_class_registry():\n", "    '''Build comprehensive class registry with all metadata'''\n", "    class_registry = {}\n", "    inter_app_calls = []\n", "    \n", "    print('🔍 Building class registry...')\n", "    java_files = list(BASE_PATH.rglob('*.java'))\n", "    \n", "    for file_path in tqdm(java_files, desc='Processing files'):\n", "        app_name = detect_application(file_path)\n", "        \n", "        try:\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                source_code_str = f.read()\n", "            \n", "            package_name, imports = extract_package_and_imports(source_code_str)\n", "            endpoints = extract_api_endpoints(source_code_str)\n", "            db_entities = extract_database_entities(source_code_str)\n", "            class_relationships = extract_class_relationships(source_code_str)\n", "            \n", "            class_name = file_path.stem\n", "            fqcn = f'{package_name}.{class_name}' if package_name else class_name\n", "            \n", "            # Extract data lineage\n", "            data_lineage = build_data_lineage_graph(source_code_str, class_name, app_name)\n", "            \n", "            # Detect inter-application dependencies\n", "            for imp in imports:\n", "                if app_name == 'ServiceBolt' and 'UnifiedBolt' in imp:\n", "                    inter_app_calls.append({\n", "                        'from_app': 'ServiceBolt',\n", "                        'to_app': 'UnifiedBolt',\n", "                        'from_class': class_name,\n", "                        'imported_class': imp.split('.')[-1],\n", "                        'import_type': 'dependency'\n", "                    })\n", "                elif app_name == 'UnifiedBolt' and 'ServiceBolt' in imp:\n", "                    inter_app_calls.append({\n", "                        'from_app': 'UnifiedBolt',\n", "                        'to_app': 'ServiceBolt',\n", "                        'from_class': class_name,\n", "                        'imported_class': imp.split('.')[-1],\n", "                        'import_type': 'dependency'\n", "                    })\n", "            \n", "            class_registry[class_name] = {\n", "                'fqcn': fqcn,\n", "                'package': package_name,\n", "                'file_path': str(file_path),\n", "                'application': app_name,\n", "                'imports': imports,\n", "                'endpoints': endpoints,\n", "                'db_entities': db_entities,\n", "                'class_relationships': class_relationships,\n", "                'data_lineage': data_lineage\n", "            }\n", "            \n", "        except Exception as e:\n", "            print(f'❌ Error processing {file_path.name}: {e}')\n", "            continue\n", "    \n", "    return class_registry, inter_app_calls\n", "\n", "class_registry, inter_app_calls = build_enhanced_class_registry()\n", "\n", "# Summary statistics\n", "total_endpoints = sum(len(info.get('endpoints', [])) for info in class_registry.values())\n", "total_db_entities = sum(len(info.get('db_entities', [])) for info in class_registry.values())\n", "total_data_lineage = sum(len(info.get('data_lineage', [])) for info in class_registry.values())\n", "\n", "print(f'✅ Class registry built:')\n", "print(f'   📁 {len(class_registry)} classes processed')\n", "print(f'   🌐 {total_endpoints} API endpoints found')\n", "print(f'   🗄️ {total_db_entities} database entities found')\n", "print(f'   📊 {total_data_lineage} data lineage patterns found')\n", "print(f'   🔗 {len(inter_app_calls)} inter-application dependencies')"]}, {"cell_type": "code", "execution_count": 28, "id": "ast_extraction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌳 Extracting AST structures...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["AST Processing: 100%|██████████| 19/19 [00:00<00:00, 333.84it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ AST extraction complete: 463 relationships extracted\n", "   Applications: {'UnifiedBolt': 368, 'ServiceBolt': 95}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== ENHANCED AST EXTRACTION ==========\n", "\n", "def extract_variable_transformations(source_code_str):\n", "    '''Extract variable transformations like a + b = c'''\n", "    transformations = []\n", "    \n", "    assignment_patterns = [\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\s*[+\\-*/]\\s*(\\w+)',  # binary operations\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\s*\\.\\s*(\\w+)\\s*\\(',  # method calls\n", "        r'(\\w+)\\s*=\\s*new\\s+(\\w+)\\s*\\(',  # object creation\n", "    ]\n", "    \n", "    for pattern in assignment_patterns:\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            if len(match) >= 3:\n", "                result_var = match[0]\n", "                input_vars = [match[1], match[2]]\n", "                \n", "                if (is_meaningful_variable(result_var) and \n", "                    all(is_meaningful_variable(var) for var in input_vars)):\n", "                    transformations.append({\n", "                        'result': result_var,\n", "                        'inputs': input_vars,\n", "                        'operation': 'transform'\n", "                    })\n", "    \n", "    return transformations\n", "\n", "def extract_enhanced_ast_structure(file_path):\n", "    '''Extract AST structure with enhanced filtering'''\n", "    records = []\n", "    source_code = read_source_code(file_path)\n", "    source_code_str = source_code.decode('utf-8')\n", "    tree = parser.parse(source_code)\n", "    root_node = tree.root_node\n", "    file_name = os.path.basename(file_path)\n", "    app_name = detect_application(file_path)\n", "    \n", "    transformations = extract_variable_transformations(source_code_str)\n", "\n", "    def clean_node_name(name):\n", "        '''Clean node names'''\n", "        if not name:\n", "            return name\n", "        \n", "        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']\n", "        for prefix in prefixes_to_remove:\n", "            if name.lower().startswith(prefix):\n", "                name = name[len(prefix):]\n", "        \n", "        name = re.sub(r'\\.(java|class)$', '', name, flags=re.IGNORECASE)\n", "        return name.strip()\n", "\n", "    def traverse(node, parent_type=None, parent_name=None, context='global'):\n", "        # Handle class declarations\n", "        if node.type == 'class_declaration':\n", "            class_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    # File -> Class relationship\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'file',\n", "                        'destination_node': class_name,\n", "                        'destination_type': 'class',\n", "                        'relationship': 'declares',\n", "                        'file_path': str(file_path),\n", "                        'application': app_name\n", "                    })\n", "                    \n", "                    # Add metadata from registry\n", "                    class_info = class_registry.get(class_name, {})\n", "                    \n", "                    # API endpoints\n", "                    for ep in class_info.get('endpoints', []):\n", "                        endpoint_name = f\"{ep['method']} {ep['path']}\"\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': endpoint_name,\n", "                            'destination_type': 'endpoint',\n", "                            'relationship': 'exposes',\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    \n", "                    # Database entities\n", "                    for entity in class_info.get('db_entities', []):\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': entity['name'],\n", "                            'destination_type': 'table',\n", "                            'relationship': 'maps_to',\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    \n", "                    # Class relationships\n", "                    for rel in class_info.get('class_relationships', []):\n", "                        if rel['child_class'] == class_name:\n", "                            records.append({\n", "                                'source_node': class_name,\n", "                                'source_type': 'class',\n", "                                'destination_node': rel['parent_class'],\n", "                                'destination_type': 'class',\n", "                                'relationship': rel['relationship_type'],\n", "                                'file_path': str(file_path),\n", "                                'application': app_name\n", "                            })\n", "                    \n", "                    # Data lineage\n", "                    for lineage in class_info.get('data_lineage', []):\n", "                        records.append({\n", "                            'source_node': lineage['source_name'],\n", "                            'source_type': lineage['source_type'],\n", "                            'destination_node': lineage['target_name'],\n", "                            'destination_type': lineage['target_type'],\n", "                            'relationship': f\"data_{lineage['operation']}\",\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    break\n", "            \n", "            # Traverse children with class context\n", "            for child in node.children:\n", "                traverse(child, 'class', class_name, 'class')\n", "                \n", "        # Handle method declarations\n", "        elif node.type == 'method_declaration':\n", "            method_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    if parent_name and parent_type in ['class', 'interface']:\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': method_name,\n", "                            'destination_type': 'method',\n", "                            'relationship': 'declares',\n", "                            'file_path': str(file_path),\n", "                            'application': app_name\n", "                        })\n", "                    break\n", "            \n", "            for child in node.children:\n", "                traverse(child, 'method', method_name, 'method')\n", "                \n", "        # Handle field declarations with filtering\n", "        elif node.type == 'field_declaration':\n", "            for child in node.children:\n", "                if child.type == 'variable_declarator':\n", "                    for grandchild in child.children:\n", "                        if grandchild.type == 'identifier':\n", "                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))\n", "                            \n", "                            if is_meaningful_variable(field_name) and parent_name and parent_type == 'class':\n", "                                records.append({\n", "                                    'source_node': parent_name,\n", "                                    'source_type': parent_type,\n", "                                    'destination_node': field_name,\n", "                                    'destination_type': 'variable',\n", "                                    'relationship': 'has_field',\n", "                                    'file_path': str(file_path),\n", "                                    'application': app_name\n", "                                })\n", "        else:\n", "            # Continue traversing\n", "            for child in node.children:\n", "                traverse(child, parent_type, parent_name, context)\n", "\n", "    traverse(root_node)\n", "    \n", "    # Add transformation relationships\n", "    for transform in transformations:\n", "        for input_var in transform['inputs']:\n", "            records.append({\n", "                'source_node': input_var,\n", "                'source_type': 'variable',\n", "                'destination_node': transform['result'],\n", "                'destination_type': 'variable',\n", "                'relationship': 'transforms_to',\n", "                'file_path': str(file_path),\n", "                'application': app_name\n", "            })\n", "    \n", "    return records\n", "\n", "# Execute AST extraction\n", "print('🌳 Extracting AST structures...')\n", "ast_records = []\n", "java_files = list(BASE_PATH.rglob('*.java'))\n", "\n", "for file_path in tqdm(java_files, desc='AST Processing'):\n", "    try:\n", "        ast_records.extend(extract_enhanced_ast_structure(file_path))\n", "    except Exception as e:\n", "        print(f'❌ Error processing {file_path.name}: {e}')\n", "        continue\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "print(f'✅ AST extraction complete: {len(df_ast)} relationships extracted')\n", "if len(df_ast) > 0:\n", "    print(f'   Applications: {df_ast[\"application\"].value_counts().to_dict()}')"]}, {"cell_type": "code", "execution_count": 37, "id": "b0f673bd", "metadata": {}, "outputs": [], "source": ["\n", "splitter = RecursiveCharacterTextSplitter.from_language(\n", "    language=LC_Language.JAVA,\n", "    chunk_size=4000,\n", "    chunk_overlap=200\n", ")\n", "\n", "java_docs, split_docs = [], []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            try:\n", "                loader = TextLoader(os.path.join(root, file))\n", "                java_docs.extend(loader.load())\n", "            except Exception as e:\n", "                print(f\" Error loading {file}: {e}\")\n", "                continue\n", "\n", "for doc in java_docs:\n", "    split_docs.extend(splitter.split_documents([doc]))"]}, {"cell_type": "code", "execution_count": 38, "id": "ab4f4d06", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 3: LLM EXTRACTION WITH AST CONTEXT ==========\n", "\n", "\n", "def build_enhanced_system_prompt(file_path, ast_df, class_registry):\n", "    ast_subset = ast_df[ast_df['file_path'] == file_path] if len(ast_df) > 0 else pd.DataFrame()\n", "    ast_context = ''\n", "    for _, row in ast_subset.iterrows():\n", "        ast_context += f\"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']}\\n\"\n", "    \n", "    registry_context = 'Known Classes:\\n'\n", "    for class_name, info in class_registry.items():\n", "        registry_context += f'- {class_name} (FQCN: {info[\"fqcn\"]})\\n'\n", "        if len(info.get('endpoints', [])) > 0:\n", "            registry_context += f'  * {len(info[\"endpoints\"])} API endpoint(s)\\n'\n", "        if len(info.get('db_entities', [])) > 0:\n", "            registry_context += f'  * {len(info[\"db_entities\"])} DB entity/entities\\n'\n", "    \n", "    prompt = f\"\"\"\n", "You are a Java code lineage extraction engine. Extract relationships between code entities with STRICT focus on:\n", "\n", "CONTEXT:\n", "{registry_context}\n", "\n", "AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):\n", "{ast_context}\n", "\n", "CRITICAL RULES - <PERSON><PERSON><PERSON><PERSON> EXACTLY:\n", "1. Use SIMPLE names only (remove prefixes like \"method:\", \"class:\", etc.)\n", "2. MANDATORY RELATIONSHIP DIRECTIONS (DO NOT REVERSE):\n", "   - file -[declares]-> class\n", "   - class -[declares]-> method  \n", "   - class -[has_field]-> variable\n", "   - method -[uses]-> variable\n", "   - class -[declares]-> endpoint\n", "   - class -[maps_to]-> table\n", "3. Extract REST API endpoints as 'endpoint' nodes (GET /api/users, POST /api/data)\n", "4. Extract database tables from @Entity, @Table, @Query annotations\n", "5. Extract interface extends and class implements relationships\n", "6. NEVER create reverse relationships (method->class, variable->method, etc.)\n", "7. Follow the AST RELATIONSHIPS above for correct structure\n", "8. Clean node names (remove \"method:\", \"class:\" prefixes)\n", "\n", "Extract triples in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the triples, no explanations.\n", "\"\"\"\n", "    return prompt"]}, {"cell_type": "code", "execution_count": null, "id": "llm_extraction", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 3: LLM EXTRACTION WITH ENHANCED VARIABLE TRACKING ==========\n", "\n", "def extract_enhanced_variable_lineage(source_code_str, class_name, method_context=None):\n", "    '''Extract variable lineage with method context for better tracking'''\n", "    variable_lineage = []\n", "    \n", "    # Enhanced variable assignment patterns with method context\n", "    assignment_patterns = [\n", "        # Method return assignments: result = methodName(...)\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\s*\\(.*?\\)',\n", "        # Service/Repository calls: result = serviceInstance.methodName(...)\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\s*\\.\\s*(\\w+)\\s*\\(',\n", "        # Object creation: result = new ClassName(...)\n", "        r'(\\w+)\\s*=\\s*new\\s+(\\w+)\\s*\\(',\n", "        # Field access: result = object.fieldName\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\s*\\.\\s*(\\w+)(?!\\s*\\()',\n", "        # Simple assignments: result = variable\n", "        r'(\\w+)\\s*=\\s*(\\w+)(?!\\s*[\\(\\.])',\n", "        # Stream operations: result = source.stream().operation(...)\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\.stream\\(\\)\\.(\\w+)\\(',\n", "    ]\n", "    \n", "    for pattern in assignment_patterns:\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            if len(match) >= 2:\n", "                result_var = match[0]\n", "                source_identifier = match[1]\n", "                operation = match[2] if len(match) >= 3 else 'assign'\n", "                \n", "                if is_meaningful_variable(result_var) and is_meaningful_variable(source_identifier):\n", "                    # Create enhanced variable name with context\n", "                    result_with_context = f\"{class_name}.{method_context or 'field'}.{result_var}\" if method_context else f\"{class_name}.{result_var}\"\n", "                    source_with_context = f\"{class_name}.{method_context or 'field'}.{source_identifier}\" if method_context else f\"{class_name}.{source_identifier}\"\n", "                    \n", "                    variable_lineage.append({\n", "                        'source_node': source_with_context,\n", "                        'source_type': 'variable',\n", "                        'destination_node': result_with_context,\n", "                        'destination_type': 'variable',\n", "                        'relationship': f'flows_to_via_{operation}',\n", "                        'operation_context': operation,\n", "                        'class_context': class_name,\n", "                        'method_context': method_context\n", "                    })\n", "    \n", "    return variable_lineage\n", "\n", "def extract_method_variable_context(source_code_str, class_name):\n", "    '''Extract method-level variable context'''\n", "    method_variables = []\n", "    \n", "    # Find method declarations and their variables\n", "    method_pattern = r'(?:public|private|protected)?\\s*(?:static)?\\s*(?:\\w+\\s+)?(\\w+)\\s*\\([^)]*\\)\\s*\\{([^}]*(?:\\{[^}]*\\}[^}]*)*)\\}'\n", "    method_matches = re.findall(method_pattern, source_code_str, re.MULTILINE | re.DOTALL)\n", "    \n", "    for method_name, method_body in method_matches:\n", "        if is_meaningful_variable(method_name):\n", "            # Extract variables within this method\n", "            method_var_lineage = extract_enhanced_variable_lineage(method_body, class_name, method_name)\n", "            method_variables.extend(method_var_lineage)\n", "    \n", "    return method_variables\n", "\n", "# Initialize LLM lineage collection\n", "all_llm_lineage = []\n", "\n", "print('🤖 Starting enhanced LLM extraction with variable context...')\n", "\n", "for chunk in tqdm(split_docs, desc='Stage 3: Enhanced LLM Extraction'):\n", "    file_path = chunk.metadata.get('source')\n", "    system_prompt = build_enhanced_system_prompt(file_path, df_ast, class_registry)\n", "    \n", "    # Extract class name from file path for context\n", "    file_name = os.path.basename(file_path) if file_path else 'unknown'\n", "    class_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name\n", "    \n", "    # Extract enhanced variable lineage with method context\n", "    enhanced_var_lineage = extract_method_variable_context(chunk.page_content, class_name)\n", "    all_llm_lineage.extend(enhanced_var_lineage)\n", "    \n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=system_prompt,\n", "        allowed_nodes=['project', 'application', 'folder', 'file', 'class', 'method', 'interface', 'variable', 'endpoint', 'data'],\n", "        allowed_relationships=[\n", "            ('file', 'declares', 'class'),\n", "            ('file', 'declares', 'interface'),\n", "            ('class', 'declares', 'method'),\n", "            ('interface', 'declares', 'method'),\n", "            ('class', 'declares', 'endpoint'),\n", "            ('method', 'calls', 'method'),\n", "            ('class', 'has_field', 'variable'),\n", "            ('method', 'uses', 'variable'),\n", "            ('class', 'uses', 'class'),\n", "            ('interface', 'extends', 'interface'),\n", "            ('class', 'extends', 'class'),\n", "            ('class', 'implements', 'interface'),\n", "            ('class', 'maps_to', 'table'),\n", "            ('method', 'reads_from', 'table'),\n", "            ('method', 'writes_to', 'table'),\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False,\n", "    )\n", "    \n", "    try:\n", "        graph_docs = transformer.convert_to_graph_documents([chunk])\n", "        for gd in graph_docs:\n", "            for rel in gd.relationships:\n", "                s_node = rel.source.id.strip()\n", "                s_type = rel.source.type.strip().lower()\n", "                t_node = rel.target.id.strip()\n", "                t_type = rel.target.type.strip().lower()\n", "                rel_type = rel.type.strip().lower()\n", "\n", "                def normalize_entity(entity_name, entity_type):\n", "                    if not entity_name:\n", "                        return entity_name\n", "                    \n", "                    # Remove prefixes\n", "                    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "                    for prefix in prefixes:\n", "                        if entity_name.lower().startswith(prefix):\n", "                            entity_name = entity_name[len(prefix):]\n", "                    \n", "                    # Remove file extensions\n", "                    entity_name = re.sub(r'\\.(java|class)$', '', entity_name, flags=re.IGNORECASE)\n", "                    \n", "                    # Clean dots for class names\n", "                    if entity_type in ['class', 'method'] and '.' in entity_name:\n", "                        entity_name = entity_name.split('.')[-1]\n", "                    \n", "                    # Match with class registry for consistency\n", "                    if entity_type == 'class':\n", "                        for class_name in class_registry.keys():\n", "                            if entity_name.lower() == class_name.lower():\n", "                                return class_name.lower()\n", "                    \n", "                    return entity_name.lower()\n", "\n", "                s_node = normalize_entity(s_node, s_type)\n", "                t_node = normalize_entity(t_node, t_type)\n", "\n", "                # Skip invalid relationships\n", "                if s_node == t_node and s_type == t_type:\n", "                    continue\n", "                if not s_node or not t_node:\n", "                    continue\n", "                \n", "                # Enforce correct relationship directions\n", "                valid_directions = {\n", "                    ('file', 'declares', 'class'),\n", "                    ('file', 'declares', 'interface'),\n", "                    ('class', 'declares', 'method'),\n", "                    ('interface', 'declares', 'method'),\n", "                    ('class', 'declares', 'endpoint'),\n", "                    ('class', 'has_field', 'variable'),\n", "                    ('method', 'uses', 'variable'),\n", "                    ('class', 'maps_to', 'table'),\n", "                    ('class', 'extends', 'class'),\n", "                    ('class', 'implements', 'interface'),\n", "                    ('interface', 'extends', 'interface'),\n", "                    ('method', 'calls', 'method'),\n", "                    ('method', 'reads_from', 'table'),\n", "                    ('method', 'writes_to', 'table')\n", "                }\n", "                \n", "                if (s_type, rel_type, t_type) not in valid_directions:\n", "                    continue\n", "\n", "                all_llm_lineage.append({\n", "                    'source_node': s_node,\n", "                    'source_type': s_type,\n", "                    'destination_node': t_node,\n", "                    'destination_type': t_type,\n", "                    'relationship': rel_type,\n", "                    'file_path': file_path,\n", "                    'application': detect_application(file_path) if file_path else 'Unknown'\n", "                })\n", "    except Exception as e:\n", "        continue\n", "\n", "df_llm = pd.DataFrame(all_llm_lineage)\n", "print(f'✅ Stage 3 Complete: {len(df_llm)} LLM relationships extracted')\n", "print(f'   📊 Enhanced variable lineage: {len([r for r in all_llm_lineage if \"flows_to_via\" in r.get(\"relationship\", \"\")])}')\n", "print(f'   🔗 Traditional relationships: {len([r for r in all_llm_lineage if \"flows_to_via\" not in r.get(\"relationship\", \"\")])}')"]}, {"cell_type": "code", "execution_count": null, "id": "variable_analysis", "metadata": {}, "outputs": [], "source": ["# ========== ENHANCED VARIABLE LINEAGE ANALYSIS ==========\n", "\n", "def analyze_variable_connections():\n", "    '''Analyze the enhanced variable connections with method context'''\n", "    if 'df_llm' not in globals() or len(df_llm) == 0:\n", "        print('⚠️ No LLM data available for variable analysis')\n", "        return\n", "    \n", "    print('\\n🔍 ENHANCED VARIABLE LINEAGE ANALYSIS')\n", "    print('=' * 60)\n", "    \n", "    # Filter variable flow relationships\n", "    variable_flows = df_llm[df_llm['relationship'].str.contains('flows_to_via', na=False)]\n", "    \n", "    if len(variable_flows) > 0:\n", "        print(f'\\n📊 Variable Flow Summary:')\n", "        print(f'   Total variable flows detected: {len(variable_flows)}')\n", "        \n", "        # Group by operation type\n", "        flow_types = variable_flows['relationship'].value_counts()\n", "        print('\\n🔄 Flow Types:')\n", "        for flow_type, count in flow_types.items():\n", "            print(f'   {flow_type}: {count} flows')\n", "        \n", "        # Show examples of enhanced variable tracking\n", "        print('\\n📝 Example Variable Flows (with method context):')\n", "        for idx, (_, row) in enumerate(variable_flows.head(10).iterrows()):\n", "            source_parts = row['source_node'].split('.')\n", "            dest_parts = row['destination_node'].split('.')\n", "            \n", "            if len(source_parts) >= 3 and len(dest_parts) >= 3:\n", "                class_name = source_parts[0]\n", "                method_name = source_parts[1]\n", "                source_var = source_parts[2]\n", "                dest_var = dest_parts[2]\n", "                operation = row['relationship'].replace('flows_to_via_', '')\n", "                \n", "                print(f'   {idx+1}. In {class_name}.{method_name}():')\n", "                print(f'      {source_var} --[{operation}]--> {dest_var}')\n", "            else:\n", "                print(f'   {idx+1}. {row[\"source_node\"]} --[{row[\"relationship\"]}]--> {row[\"destination_node\"]}')\n", "        \n", "        # Application-wise variable flow analysis\n", "        if 'application' in variable_flows.columns:\n", "            app_flows = variable_flows['application'].value_counts()\n", "            print('\\n🏢 Variable Flows by Application:')\n", "            for app, count in app_flows.items():\n", "                print(f'   {app}: {count} variable flows')\n", "    else:\n", "        print('\\n⚠️ No enhanced variable flows detected')\n", "        print('   This could mean:')\n", "        print('   - The code doesn\\'t have complex variable transformations')\n", "        print('   - The regex patterns need adjustment for this codebase')\n", "        print('   - The LLM extraction didn\\'t capture variable flows')\n", "\n", "# Run the analysis\n", "analyze_variable_connections()"]}, {"cell_type": "code", "execution_count": null, "id": "data_combination", "metadata": {}, "outputs": [], "source": ["# ========== DATA COMBINATION AND NEO4J LOADING ==========\n", "\n", "def combine_clean_enhanced_data():\n", "    '''Combine all data sources with intelligent filtering'''\n", "    all_records = []\n", "    \n", "    # Add folder and file relationships\n", "    all_records.extend(folder_records)\n", "    all_records.extend(file_records)\n", "    all_records.extend(app_records)\n", "    print(f'📁 Added {len(folder_records + file_records + app_records)} folder/file relationships')\n", "    \n", "    # Add clean AST relationships (filter noise)\n", "    clean_ast_records = []\n", "    for record in ast_records:\n", "        # Include structural relationships and data lineage\n", "        if (record['relationship'] in ['declares', 'exposes', 'maps_to', 'extends', 'implements'] or \n", "            record['relationship'].startswith('data_')):\n", "            clean_ast_records.append(record)\n", "        # Include meaningful field relationships\n", "        elif (record['relationship'] == 'has_field' and \n", "              is_meaningful_variable(record['destination_node'])):\n", "            clean_ast_records.append(record)\n", "        # Include meaningful transformations\n", "        elif record['relationship'] == 'transforms_to':\n", "            clean_ast_records.append(record)\n", "    \n", "    all_records.extend(clean_ast_records)\n", "    print(f'🌳 Added {len(clean_ast_records)} clean AST relationships (filtered from {len(ast_records)})')\n", "    \n", "    # Add filtered LLM relationships\n", "    llm_records_clean = []\n", "    if 'df_llm' in globals() and len(df_llm) > 0:\n", "        for _, row in df_llm.iterrows():\n", "            # Filter meaningful LLM relationships including enhanced variable flows\n", "            if (row['relationship'].upper() in ['DECLARES', 'EXTENDS', 'IMPLEMENTS', 'USES', 'CALLS', 'RETURNS'] or\n", "                'flows_to_via' in row['relationship'].lower()):\n", "                if (is_meaningful_variable(row['source_node']) and \n", "                    is_meaningful_variable(row['destination_node'])):\n", "                    llm_records_clean.append(row.to_dict())\n", "        \n", "        all_records.extend(llm_records_clean)\n", "        print(f'🤖 Added {len(llm_records_clean)} filtered LLM relationships (from {len(df_llm)})')\n", "    else:\n", "        print('🤖 No LLM relationships to add (df_llm not available or empty)')\n", "    \n", "    # Add inter-application relationships\n", "    for call in inter_app_calls:\n", "        all_records.append({\n", "            'source_node': call['from_class'],\n", "            'source_type': 'class',\n", "            'destination_node': call['imported_class'],\n", "            'destination_type': 'class',\n", "            'relationship': 'depends_on',\n", "            'file_path': None,\n", "            'application': call['from_app']\n", "        })\n", "    \n", "    print(f'🔗 Added {len(inter_app_calls)} inter-application dependencies')\n", "    \n", "    return pd.DataFrame(all_records)\n", "\n", "def load_to_neo4j(df_final):\n", "    '''Load data to Neo4j with error handling'''\n", "    if graph is None:\n", "        print('❌ Neo4j connection not available')\n", "        return\n", "    \n", "    try:\n", "        # Clear existing data\n", "        print('🧹 Clearing existing data...')\n", "        graph.query('MATCH (n) DETACH DELETE n')\n", "        \n", "        # Create nodes and relationships\n", "        print('📊 Loading nodes and relationships...')\n", "        \n", "        # Group by relationship type for efficient loading\n", "        relationship_groups = df_final.groupby('relationship')\n", "        \n", "        for rel_type, group in tqdm(relationship_groups, desc='Loading relationships'):\n", "            for _, row in group.iterrows():\n", "                try:\n", "                    # Create nodes and relationship\n", "                    cypher = f\"\"\"\n", "                    MERGE (source:{row['source_type'].title()} {{name: $source_name, application: $app}})\n", "                    MERGE (target:{row['destination_type'].title()} {{name: $target_name, application: $app}})\n", "                    MERGE (source)-[:{rel_type.upper().replace(' ', '_')}]->(target)\n", "                    \"\"\"\n", "                    \n", "                    graph.query(cypher, {\n", "                        'source_name': str(row['source_node']),\n", "                        'target_name': str(row['destination_node']),\n", "                        'app': str(row['application'])\n", "                    })\n", "                \n", "                except Exception as e:\n", "                    print(f'⚠️ Error loading relationship: {e}')\n", "                    continue\n", "        \n", "        # Create indexes for performance\n", "        indexes = [\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:File) ON (n.name)',\n", "            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)'\n", "        ]\n", "        \n", "        for index in indexes:\n", "            try:\n", "                graph.query(index)\n", "            except:\n", "                pass\n", "        \n", "        print('✅ Data successfully loaded to Neo4j')\n", "        \n", "        # Get statistics\n", "        stats = graph.query(\"\"\"\n", "        MATCH (n)\n", "        RETURN labels(n)[0] as node_type, count(*) as count\n", "        ORDER BY count DESC\n", "        \"\"\")\n", "        \n", "        print('\\n📊 Neo4j Statistics:')\n", "        for stat in stats:\n", "            print(f'   {stat[\"node_type\"]}: {stat[\"count\"]} nodes')\n", "    \n", "    except Exception as e:\n", "        print(f'❌ Error loading to Neo4j: {e}')\n", "\n", "# Execute data combination and loading\n", "print('\\n🔄 Combining all data sources...')\n", "df_final = combine_clean_enhanced_data()\n", "\n", "print(f'\\n✅ Final dataset created:')\n", "print(f'   📊 Total relationships: {len(df_final)}')\n", "print(f'   🏢 Applications: {df_final[\"application\"].value_counts().to_dict()}')\n", "print(f'   🔗 Relationship types: {len(df_final[\"relationship\"].unique())}')\n", "\n", "# Load to Neo4j\n", "print('\\n🚀 Loading to Neo4j...')\n", "load_to_neo4j(df_final)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 5}