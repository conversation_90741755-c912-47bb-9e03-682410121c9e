variable_name,variable_type,defined_in_method,defined_in_class,defined_in_file,line_number,scope,is_parameter,assigned_from,assigned_to,used_in_methods,used_in_classes,data_source,data_sink,transformation_logic,application
buildtoolservice,BuildToolService,,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,47,class_field,False,,,,,,,,ServiceBolt
ctx,AnnotationConfigApplicationContext,,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,50,class_field,False,,,,,,,,ServiceBolt
buildtoolservice,BuildToolService,BuildToolController,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,54,method_parameter,True,,,,,,,,ServiceBolt
buildtoolservice,,BuildToolController,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,55,method_local,False,buildtoolservice,,,,,,,ServiceBolt
projectName,String,getBuildType,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,146,method_parameter,True,,,,,,,,ServiceBolt
buildType,String,getBuildType,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,147,method_local,False,"""""",,,,,,,ServiceBolt
buildType,,getBuildType,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,147,method_local,False,"""""",,,,,,,ServiceBolt
metric1,ConfigurationToolInfoMetric,next,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,155,method_local,False,(ConfigurationToolInfoMetric) configuration1,,,,,,,ServiceBolt
metric1,,next,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,155,method_local,False,(ConfigurationToolInfoMetric) configuration1,,,,,,,ServiceBolt
buildType,,if,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,158,method_local,False,"""TEAMCITY""",,,,,,,ServiceBolt
buildType,,if,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,161,method_local,False,"""JENKINS""",,,,,,,ServiceBolt
buildType,,if,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,164,method_local,False,"""CircleCI""",,,,,,,ServiceBolt
"Build"".equals(metric1.getToolName(","""TFS",if,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,166,method_parameter,True,,,,,,,,ServiceBolt
buildType,,if,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,167,method_local,False,"""TFSBUILD""",,,,,,,ServiceBolt
buildType,return,close,BuildToolController,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java,173,method_local,False,,,,,,,,ServiceBolt
buildToolRepository,BuildToolRep,,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,34,class_field,False,,,,,,,,ServiceBolt
buildFailurePatternForProjectRepo,BuildFailurePatternForProjectRepo,,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,35,class_field,False,,,,,,,,ServiceBolt
configSettingRepo,ConfigurationSettingRep,getLogger,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,37,method_local,False,,,,,,,,ServiceBolt
metric,ConfigurationToolInfoMetric,getLogger,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,38,method_local,False,,,,,,,,ServiceBolt
noDataConst,String,getLogger,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,39,method_local,False,"""no datas found in DB for project  """,,,,,,,ServiceBolt
noDataConst,,getLogger,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,39,method_local,False,"""no datas found in DB for project  """,,,,,,,ServiceBolt
buildConst,String,getLogger,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,40,method_local,False,"""build""",,,,,,,ServiceBolt
buildConst,,getLogger,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,40,method_local,False,"""build""",,,,,,,ServiceBolt
buildToolRepository,,getLogger,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,46,method_local,False,buildToolRepository,,,,,,,ServiceBolt
buildFailurePatternForProjectRepo,,getLogger,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,47,method_local,False,buildFailurePatternForProjectRepo,,,,,,,ServiceBolt
configSettingRepo,,getLogger,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,48,method_local,False,configSettingRepo,,,,,,,ServiceBolt
projectName,String,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,51,method_parameter,True,,,,,,,,ServiceBolt
lastUpdate,long,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,53,method_local,False,1,,,,,,,ServiceBolt
lastUpdate,,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,53,method_local,False,1,,,,,,,ServiceBolt
projectName,String,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,64,method_parameter,True,,,,,,,,ServiceBolt
toolName,String,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,64,method_parameter,True,,,,,,,,ServiceBolt
lastUpdate,long,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,67,method_local,False,1,,,,,,,ServiceBolt
lastUpdate,,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,67,method_local,False,1,,,,,,,ServiceBolt
buildType,String,searchJobList,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,82,method_parameter,True,,,,,,,,ServiceBolt
projectName,String,searchJobList,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,82,method_parameter,True,,,,,,,,ServiceBolt
lastUpdate,long,searchJobList,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,83,method_local,False,1,,,,,,,ServiceBolt
lastUpdate,,searchJobList,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,83,method_local,False,1,,,,,,,ServiceBolt
buildType,String,searchForTest,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,94,method_parameter,True,,,,,,,,ServiceBolt
projectName,String,searchForTest,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,94,method_parameter,True,,,,,,,,ServiceBolt
result,return,findByBuildTypeAndName,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,97,method_local,False,,,,,,,,ServiceBolt
projectName,String,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,103,method_parameter,True,,,,,,,,ServiceBolt
sDate,long,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,103,method_parameter,True,,,,,,,,ServiceBolt
eDate,long,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,103,method_parameter,True,,,,,,,,ServiceBolt
flag,boolean,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,103,method_parameter,True,,,,,,,,ServiceBolt
flagnew,boolean,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,104,method_local,False,flag,,,,,,,ServiceBolt
flagnew,,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,104,method_local,False,flag,,,,,,,ServiceBolt
toolName,String,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,105,method_local,False,null,,,,,,,ServiceBolt
toolName,,search,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,105,method_local,False,null,,,,,,,ServiceBolt
m:config.get(0,ConfigurationToolInfoMetric,for,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,108,method_parameter,True,,,,,,,,ServiceBolt
metric,,if,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,110,method_local,False,m,,,,,,,ServiceBolt
toolName,,if,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,112,method_local,False,"""GITLAB""",,,,,,,ServiceBolt
toolName,,if,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,114,method_local,False,"""BITBUCKET""",,,,,,,ServiceBolt
toolName,,if,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,116,method_local,False,"""JENKINS""",,,,,,,ServiceBolt
projectName,noDataConst+,info,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,137,method_parameter,True,,,,,,,,ServiceBolt
req,BuildFailureRequest,for,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,154,method_parameter,True,,,,,,,,ServiceBolt
buildFailurePatternForProjectList,,,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,159,class_field,False,buildFailurePatternForProjectRepo,,,,,,,ServiceBolt
buildFailurePatternForProject,BuildFailurePatternForProjectInJenkinsModel,save,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,167,method_local,False,buildFailurePatternForProjectList,,,,,,,ServiceBolt
buildFailurePatternForProject,,save,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,167,method_local,False,buildFailurePatternForProjectList,,,,,,,ServiceBolt
lastUpdated,long,fetchBuildData,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,180,method_local,False,1,,,,,,,ServiceBolt
lastUpdated,,fetchBuildData,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,180,method_local,False,1,,,,,,,ServiceBolt
datas,"""no",info,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,185,method_parameter,True,,,,,,,,ServiceBolt
projectName,String,fetchFailurePatternData,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,191,method_parameter,True,,,,,,,,ServiceBolt
lastUpdated,long,fetchFailurePatternData,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,192,method_local,False,1,,,,,,,ServiceBolt
lastUpdated,,fetchFailurePatternData,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,192,method_local,False,1,,,,,,,ServiceBolt
response,,fetchFailurePatternData,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,193,method_local,False,buildFailurePatternForProjectRepo,,,,,,,ServiceBolt
projName,String,getOneByProjectName,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,205,method_parameter,True,,,,,,,,ServiceBolt
result,return,findOneByNameOrderByBuildIDDesc,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,208,method_local,False,,,,,,,,ServiceBolt
projName,String,getBuildDetailsHome,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,213,method_parameter,True,,,,,,,,ServiceBolt
almType,String,getBuildDetailsHome,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,213,method_parameter,True,,,,,,,,ServiceBolt
result,return,getBuildDetailsHome,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,217,method_local,False,,,,,,,,ServiceBolt
proName,String,getValueStream,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,222,method_parameter,True,,,,,,,,ServiceBolt
m:config.get(0,ConfigurationToolInfoMetric,for,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,226,method_parameter,True,,,,,,,,ServiceBolt
metric,,if,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,228,method_local,False,m,,,,,,,ServiceBolt
proName,String,getGitlabValueStream,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,242,method_parameter,True,,,,,,,,ServiceBolt
m:config.get(0,ConfigurationToolInfoMetric,for,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,246,method_parameter,True,,,,,,,,ServiceBolt
metric,,if,BuildToolServiceImplemantation,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java,248,method_local,False,m,,,,,,,ServiceBolt
id,ObjectId,,BaseModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java,9,class_field,False,,,,,,,,UnifiedBolt
id,return,getId,BaseModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java,12,method_local,False,,,,,,,,UnifiedBolt
id,ObjectId,setId,BaseModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java,15,method_parameter,True,,,,,,,,UnifiedBolt
id,,setId,BaseModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java,16,method_local,False,id,,,,,,,UnifiedBolt
collection,,,,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,15,global,False,"""BuildFailurePatternForProject"")",,,,,,,UnifiedBolt
projectName,String,,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,18,class_field,False,,,,,,,,UnifiedBolt
userName,String,,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,19,class_field,False,,,,,,,,UnifiedBolt
timestampOfCreation,long,ArrayList,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,21,method_local,False,,,,,,,,UnifiedBolt
projectName,return,getProjectName,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,34,method_local,False,,,,,,,,UnifiedBolt
projectName,String,setProjectName,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,41,method_parameter,True,,,,,,,,UnifiedBolt
projectName,,setProjectName,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,42,method_local,False,projectName,,,,,,,UnifiedBolt
patternMetrics,return,getPatternMetrics,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,53,method_local,False,,,,,,,,UnifiedBolt
patternMetrics,List<BuildFailurePatternMetrics>,setPatternMetrics,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,60,method_parameter,True,,,,,,,,UnifiedBolt
patternMetrics,,setPatternMetrics,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,61,method_local,False,patternMetrics,,,,,,,UnifiedBolt
timestampOfCreation,return,getTimestampOfCreation,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,68,method_local,False,,,,,,,,UnifiedBolt
timestampOfCreation,long,setTimestampOfCreation,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,75,method_parameter,True,,,,,,,,UnifiedBolt
timestampOfCreation,,setTimestampOfCreation,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,76,method_local,False,timestampOfCreation,,,,,,,UnifiedBolt
patternDefined,String,addPatternMetric,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,85,method_parameter,True,,,,,,,,UnifiedBolt
patternDisplayed,String,addPatternMetric,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,85,method_parameter,True,,,,,,,,UnifiedBolt
patternCount,long,addPatternMetric,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,85,method_parameter,True,,,,,,,,UnifiedBolt
patternDefined,String,addPatternMetric,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,93,method_parameter,True,,,,,,,,UnifiedBolt
patternDisplayed,String,addPatternMetric,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,93,method_parameter,True,,,,,,,,UnifiedBolt
userName,return,getUserName,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,101,method_local,False,,,,,,,,UnifiedBolt
userName,String,setUserName,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,104,method_parameter,True,,,,,,,,UnifiedBolt
userName,,setUserName,BuildFailurePatternForProjectInJenkinsModel,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,105,method_local,False,userName,,,,,,,UnifiedBolt
patternDefined,String,,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,12,class_field,False,,,,,,,,UnifiedBolt
patternDisplayed,String,,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,13,class_field,False,,,,,,,,UnifiedBolt
patternCount,long,,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,14,class_field,False,,,,,,,,UnifiedBolt
repoName,String,,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,15,class_field,False,,,,,,,,UnifiedBolt
patternDefined,return,getPatternDefined,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,28,method_local,False,,,,,,,,UnifiedBolt
patternDefined,String,setPatternDefined,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,35,method_parameter,True,,,,,,,,UnifiedBolt
patternDefined,,setPatternDefined,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,36,method_local,False,patternDefined,,,,,,,UnifiedBolt
patternDisplayed,return,getPatternDisplayed,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,43,method_local,False,,,,,,,,UnifiedBolt
patternDisplayed,String,setPatternDisplayed,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,50,method_parameter,True,,,,,,,,UnifiedBolt
patternDisplayed,,setPatternDisplayed,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,51,method_local,False,patternDisplayed,,,,,,,UnifiedBolt
patternCount,return,getPatternCount,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,58,method_local,False,,,,,,,,UnifiedBolt
patternCount,long,setPatternCount,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,65,method_parameter,True,,,,,,,,UnifiedBolt
patternCount,,setPatternCount,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,66,method_local,False,patternCount,,,,,,,UnifiedBolt
repoName,return,getRepoName,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,70,method_local,False,,,,,,,,UnifiedBolt
repoName,String,setRepoName,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,73,method_parameter,True,,,,,,,,UnifiedBolt
repoName,,setRepoName,BuildFailurePatternMetrics,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java,74,method_local,False,repoName,,,,,,,UnifiedBolt
fileNames,String,,BuildFileInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,4,class_field,False,,,,,,,,UnifiedBolt
editType,String,,BuildFileInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,5,class_field,False,,,,,,,,UnifiedBolt
fileNames,return,getFileNames,BuildFileInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,8,method_local,False,,,,,,,,UnifiedBolt
fileNames,String,setFileNames,BuildFileInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,11,method_parameter,True,,,,,,,,UnifiedBolt
fileNames,,setFileNames,BuildFileInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,12,method_local,False,fileNames,,,,,,,UnifiedBolt
editType,return,getEditType,BuildFileInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,16,method_local,False,,,,,,,,UnifiedBolt
editType,String,setEditType,BuildFileInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,19,method_parameter,True,,,,,,,,UnifiedBolt
editType,,setEditType,BuildFileInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java,20,method_local,False,editType,,,,,,,UnifiedBolt
message,String,,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,6,class_field,False,,,,,,,,UnifiedBolt
committer,String,,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,7,class_field,False,,,,,,,,UnifiedBolt
message,return,getMessage,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,11,method_local,False,,,,,,,,UnifiedBolt
message,String,setMessage,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,14,method_parameter,True,,,,,,,,UnifiedBolt
message,,setMessage,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,15,method_local,False,message,,,,,,,UnifiedBolt
committer,return,getCommitter,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,19,method_local,False,,,,,,,,UnifiedBolt
committer,String,setCommitter,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,22,method_parameter,True,,,,,,,,UnifiedBolt
committer,,setCommitter,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,23,method_local,False,committer,,,,,,,UnifiedBolt
buildFileInfoList,return,getBuildFileInfoList,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,27,method_local,False,,,,,,,,UnifiedBolt
buildFileInfoList,List<BuildFileInfo>,setBuildFileInfoList,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,30,method_parameter,True,,,,,,,,UnifiedBolt
buildFileInfoList,,setBuildFileInfoList,BuildInfo,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java,31,method_local,False,buildFileInfoList,,,,,,,UnifiedBolt
stepName,String,,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,4,class_field,False,,,,,,,,UnifiedBolt
duration,long,,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,5,class_field,False,,,,,,,,UnifiedBolt
result,String,,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,6,class_field,False,,,,,,,,UnifiedBolt
startedTime,long,,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,7,class_field,False,,,,,,,,UnifiedBolt
completedTime,long,,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,8,class_field,False,,,,,,,,UnifiedBolt
stepName,return,getStepName,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,11,method_local,False,,,,,,,,UnifiedBolt
stepName,String,setStepName,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,14,method_parameter,True,,,,,,,,UnifiedBolt
stepName,,setStepName,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,15,method_local,False,stepName,,,,,,,UnifiedBolt
duration,return,getDuration,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,19,method_local,False,,,,,,,,UnifiedBolt
duration,long,setDuration,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,22,method_parameter,True,,,,,,,,UnifiedBolt
duration,,setDuration,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,23,method_local,False,duration,,,,,,,UnifiedBolt
result,return,getResult,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,27,method_local,False,,,,,,,,UnifiedBolt
result,String,setResult,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,30,method_parameter,True,,,,,,,,UnifiedBolt
result,,setResult,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,31,method_local,False,result,,,,,,,UnifiedBolt
startedTime,return,getStartedTime,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,35,method_local,False,,,,,,,,UnifiedBolt
startedTime,long,setStartedTime,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,38,method_parameter,True,,,,,,,,UnifiedBolt
startedTime,,setStartedTime,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,39,method_local,False,startedTime,,,,,,,UnifiedBolt
completedTime,return,getCompletedTime,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,43,method_local,False,,,,,,,,UnifiedBolt
completedTime,long,setCompletedTime,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,46,method_parameter,True,,,,,,,,UnifiedBolt
completedTime,,setCompletedTime,BuildSteps,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java,47,method_local,False,completedTime,,,,,,,UnifiedBolt
collection,,,,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,18,global,False,"""Build"")",,,,,,,UnifiedBolt
collectorItemId,ObjectId,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,21,class_field,False,,,,,,,,UnifiedBolt
timestamp,long,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,22,class_field,False,,,,,,,,UnifiedBolt
timeString,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,23,class_field,False,,,,,,,,UnifiedBolt
name,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,24,class_field,False,,,,,,,,UnifiedBolt
jobName,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,25,class_field,False,,,,,,,,UnifiedBolt
url,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,26,class_field,False,,,,,,,,UnifiedBolt
version,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,27,class_field,False,,,,,,,,UnifiedBolt
buildType,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,28,class_field,False,,,,,,,,UnifiedBolt
buildID,long,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,29,class_field,False,,,,,,,,UnifiedBolt
metrics,,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,31,class_field,False,new HashSet<BuildToolMetric>(),,,,,,,UnifiedBolt
jobCount,int,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,34,class_field,False,,,,,,,,UnifiedBolt
createdBy,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,35,class_field,False,,,,,,,,UnifiedBolt
branchName,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,36,class_field,False,,,,,,,,UnifiedBolt
repoName,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,37,class_field,False,,,,,,,,UnifiedBolt
groupName,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,38,class_field,False,,,,,,,,UnifiedBolt
triggerType,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,39,class_field,False,,,,,,,,UnifiedBolt
definitionId,String,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,40,class_field,False,,,,,,,,UnifiedBolt
timeString,return,getTimeString,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,44,method_local,False,,,,,,,,UnifiedBolt
timeString,String,setTimeString,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,47,method_parameter,True,,,,,,,,UnifiedBolt
timeString,,setTimeString,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,48,method_local,False,timeString,,,,,,,UnifiedBolt
collectorItemId,return,getCollectorItemId,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,52,method_local,False,,,,,,,,UnifiedBolt
collectorItemId,ObjectId,setCollectorItemId,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,55,method_parameter,True,,,,,,,,UnifiedBolt
collectorItemId,,setCollectorItemId,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,56,method_local,False,collectorItemId,,,,,,,UnifiedBolt
stepsList,return,getStepsList,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,60,method_local,False,,,,,,,,UnifiedBolt
stepsList,List<BuildSteps>,setStepsList,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,63,method_parameter,True,,,,,,,,UnifiedBolt
stepsList,,setStepsList,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,64,method_local,False,stepsList,,,,,,,UnifiedBolt
createdBy,return,getCreatedBy,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,68,method_local,False,,,,,,,,UnifiedBolt
createdBy,String,setCreatedBy,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,71,method_parameter,True,,,,,,,,UnifiedBolt
createdBy,,setCreatedBy,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,72,method_local,False,createdBy,,,,,,,UnifiedBolt
branchName,return,getBranchName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,76,method_local,False,,,,,,,,UnifiedBolt
branchName,String,setBranchName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,79,method_parameter,True,,,,,,,,UnifiedBolt
branchName,,setBranchName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,80,method_local,False,branchName,,,,,,,UnifiedBolt
metrics,Set<BuildToolMetric>,setMetrics,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,83,method_parameter,True,,,,,,,,UnifiedBolt
metrics,,setMetrics,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,84,method_local,False,metrics,,,,,,,UnifiedBolt
patternDetails,BuildFailurePatternForProjectInJenkinsModel,,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,86,class_field,False,,,,,,,,UnifiedBolt
buildID,return,getBuildID,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,89,method_local,False,,,,,,,,UnifiedBolt
buildID,long,setBuildID,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,92,method_parameter,True,,,,,,,,UnifiedBolt
buildID,,setBuildID,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,93,method_local,False,buildID,,,,,,,UnifiedBolt
collectorItemId,return,getJenkinsItemId,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,101,method_local,False,,,,,,,,UnifiedBolt
jenkinsItemId,ObjectId,setJenkinsItemId,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,108,method_parameter,True,,,,,,,,UnifiedBolt
collectorItemId,,setJenkinsItemId,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,109,method_local,False,jenkinsItemId,,,,,,,UnifiedBolt
timestamp,return,getTimestamp,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,118,method_local,False,,,,,,,,UnifiedBolt
timestamp,long,setTimestamp,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,126,method_parameter,True,,,,,,,,UnifiedBolt
timestamp,,setTimestamp,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,127,method_local,False,timestamp,,,,,,,UnifiedBolt
name,return,getName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,135,method_local,False,,,,,,,,UnifiedBolt
name,String,setName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,142,method_parameter,True,,,,,,,,UnifiedBolt
name,,setName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,143,method_local,False,name,,,,,,,UnifiedBolt
url,return,getUrl,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,152,method_local,False,,,,,,,,UnifiedBolt
url,String,setUrl,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,160,method_parameter,True,,,,,,,,UnifiedBolt
url,,setUrl,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,161,method_local,False,url,,,,,,,UnifiedBolt
version,return,getVersion,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,169,method_local,False,,,,,,,,UnifiedBolt
version,String,setVersion,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,176,method_parameter,True,,,,,,,,UnifiedBolt
version,,setVersion,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,177,method_local,False,version,,,,,,,UnifiedBolt
metrics,return,getMetrics,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,184,method_local,False,,,,,,,,UnifiedBolt
buildType,return,getBuildType,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,188,method_local,False,,,,,,,,UnifiedBolt
buildType,String,setBuildType,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,191,method_parameter,True,,,,,,,,UnifiedBolt
buildType,,setBuildType,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,192,method_local,False,buildType,,,,,,,UnifiedBolt
buildInfoList,return,getBuildInfoList,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,200,method_local,False,,,,,,,,UnifiedBolt
buildInfoList,List<BuildInfo>,setBuildInfoList,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,203,method_parameter,True,,,,,,,,UnifiedBolt
buildInfoList,,setBuildInfoList,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,204,method_local,False,buildInfoList,,,,,,,UnifiedBolt
jobName,return,getJobName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,208,method_local,False,,,,,,,,UnifiedBolt
jobName,String,setJobName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,211,method_parameter,True,,,,,,,,UnifiedBolt
jobName,,setJobName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,212,method_local,False,jobName,,,,,,,UnifiedBolt
jobList,return,getJobList,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,216,method_local,False,,,,,,,,UnifiedBolt
jobList,List<String>,setJobList,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,219,method_parameter,True,,,,,,,,UnifiedBolt
jobList,,setJobList,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,220,method_local,False,jobList,,,,,,,UnifiedBolt
jobCount,return,getJobCount,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,224,method_local,False,,,,,,,,UnifiedBolt
jobCount,int,setJobCount,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,227,method_parameter,True,,,,,,,,UnifiedBolt
jobCount,,setJobCount,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,228,method_local,False,jobCount,,,,,,,UnifiedBolt
patternDetails,return,getPatternDetails,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,235,method_local,False,,,,,,,,UnifiedBolt
patternDetails,BuildFailurePatternForProjectInJenkinsModel,setPatternDetails,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,242,method_parameter,True,,,,,,,,UnifiedBolt
patternDetails,,setPatternDetails,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,243,method_local,False,patternDetails,,,,,,,UnifiedBolt
BuildTool,final,compareTo,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,246,method_parameter,True,,,,,,,,UnifiedBolt
triggerType,return,getTriggerType,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,251,method_local,False,,,,,,,,UnifiedBolt
triggerType,String,setTriggerType,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,254,method_parameter,True,,,,,,,,UnifiedBolt
triggerType,,setTriggerType,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,255,method_local,False,triggerType,,,,,,,UnifiedBolt
repoName,return,getRepoName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,259,method_local,False,,,,,,,,UnifiedBolt
repoName,String,setRepoName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,262,method_parameter,True,,,,,,,,UnifiedBolt
repoName,,setRepoName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,263,method_local,False,repoName,,,,,,,UnifiedBolt
groupName,return,getGroupName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,267,method_local,False,,,,,,,,UnifiedBolt
groupName,String,setGroupName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,270,method_parameter,True,,,,,,,,UnifiedBolt
groupName,,setGroupName,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,271,method_local,False,groupName,,,,,,,UnifiedBolt
definitionId,return,getDefinitionId,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,275,method_local,False,,,,,,,,UnifiedBolt
definitionId,String,setDefinitionId,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,278,method_parameter,True,,,,,,,,UnifiedBolt
definitionId,,setDefinitionId,BuildTool,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java,279,method_local,False,definitionId,,,,,,,UnifiedBolt
name,String,,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,10,class_field,False,,,,,,,,UnifiedBolt
valueBuildTool,Object,,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,11,class_field,False,,,,,,,,UnifiedBolt
formattedValueBuildTool,String,,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,12,class_field,False,,,,,,,,UnifiedBolt
statusMessageBuildTool,String,,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,13,class_field,False,,,,,,,,UnifiedBolt
name,String,BuildToolMetric,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,15,method_parameter,True,,,,,,,,UnifiedBolt
name,,BuildToolMetric,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,16,method_local,False,name,,,,,,,UnifiedBolt
name,return,getName,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,23,method_local,False,,,,,,,,UnifiedBolt
name,String,setName,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,30,method_parameter,True,,,,,,,,UnifiedBolt
name,,setName,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,31,method_local,False,name,,,,,,,UnifiedBolt
valueBuildTool,return,getValue,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,38,method_local,False,,,,,,,,UnifiedBolt
valueBuildTool,,setValue,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,46,method_local,False,value,,,,,,,UnifiedBolt
formattedValueBuildTool,return,getFormattedValue,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,53,method_local,False,,,,,,,,UnifiedBolt
formattedValue,String,setFormattedValue,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,60,method_parameter,True,,,,,,,,UnifiedBolt
formattedValueBuildTool,,setFormattedValue,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,61,method_local,False,formattedValue,,,,,,,UnifiedBolt
statusMessageBuildTool,return,getStatusMessage,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,68,method_local,False,,,,,,,,UnifiedBolt
statusMessage,String,setStatusMessage,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,75,method_parameter,True,,,,,,,,UnifiedBolt
statusMessageBuildTool,,setStatusMessage,BuildToolMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java,76,method_local,False,statusMessage,,,,,,,UnifiedBolt
collection,,,,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,8,global,False,"""Configuration"")",,,,,,,UnifiedBolt
timestamp,long,,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,11,class_field,False,,,,,,,,UnifiedBolt
baseline,boolean,,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,12,class_field,False,,,,,,,,UnifiedBolt
projectName,String,,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,13,class_field,False,,,,,,,,UnifiedBolt
addFlag,boolean,,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,14,class_field,False,,,,,,,,UnifiedBolt
projectType,String,,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,15,class_field,False,,,,,,,,UnifiedBolt
manualData,boolean,,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,16,class_field,False,,,,,,,,UnifiedBolt
metric,,,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,17,class_field,False,new HashSet<>(),,,,,,,UnifiedBolt
manualData,return,isManualData,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,20,method_local,False,,,,,,,,UnifiedBolt
manualData,boolean,setManualData,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,24,method_parameter,True,,,,,,,,UnifiedBolt
manualData,,setManualData,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,25,method_local,False,manualData,,,,,,,UnifiedBolt
manualData,,ConfigurationSetting,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,30,method_local,False,false,,,,,,,UnifiedBolt
timestamp,return,getTimestamp,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,36,method_local,False,,,,,,,,UnifiedBolt
timestamp,long,setTimestamp,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,39,method_parameter,True,,,,,,,,UnifiedBolt
timestamp,,setTimestamp,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,41,method_local,False,timestamp,,,,,,,UnifiedBolt
projectName,return,getProjectName,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,45,method_local,False,,,,,,,,UnifiedBolt
projectName,String,setProjectName,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,48,method_parameter,True,,,,,,,,UnifiedBolt
projectName,,setProjectName,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,49,method_local,False,projectName,,,,,,,UnifiedBolt
metric,return,getMetrics,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,53,method_local,False,,,,,,,,UnifiedBolt
addFlag,return,isAddFlag,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,57,method_local,False,,,,,,,,UnifiedBolt
addFlag,boolean,setAddFlag,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,60,method_parameter,True,,,,,,,,UnifiedBolt
addFlag,,setAddFlag,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,61,method_local,False,addFlag,,,,,,,UnifiedBolt
baseline,return,isBaseline,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,65,method_local,False,,,,,,,,UnifiedBolt
baseline,boolean,setBaseline,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,68,method_parameter,True,,,,,,,,UnifiedBolt
baseline,,setBaseline,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,69,method_local,False,baseline,,,,,,,UnifiedBolt
projectType,return,getProjectType,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,73,method_local,False,,,,,,,,UnifiedBolt
projectType,String,setProjectType,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,76,method_parameter,True,,,,,,,,UnifiedBolt
projectType,,setProjectType,ConfigurationSetting,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java,77,method_local,False,projectType,,,,,,,UnifiedBolt
selected,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,4,class_field,False,,,,,,,,UnifiedBolt
id,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,5,class_field,False,,,,,,,,UnifiedBolt
toolName,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,6,class_field,False,,,,,,,,UnifiedBolt
url,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,7,class_field,False,,,,,,,,UnifiedBolt
userName,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,8,class_field,False,,,,,,,,UnifiedBolt
password,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,9,class_field,False,,,,,,,,UnifiedBolt
toolType,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,10,class_field,False,,,,,,,,UnifiedBolt
widgetName,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,11,class_field,False,,,,,,,,UnifiedBolt
jobName,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,12,class_field,False,,,,,,,,UnifiedBolt
projectCode,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,13,class_field,False,,,,,,,,UnifiedBolt
domain,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,14,class_field,False,,,,,,,,UnifiedBolt
host,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,15,class_field,False,,,,,,,,UnifiedBolt
port,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,16,class_field,False,,,,,,,,UnifiedBolt
dbType,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,17,class_field,False,,,,,,,,UnifiedBolt
schema,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,18,class_field,False,,,,,,,,UnifiedBolt
repoName,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,19,class_field,False,,,,,,,,UnifiedBolt
secret,String,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,20,class_field,False,,,,,,,,UnifiedBolt
manualData,boolean,,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,21,class_field,False,,,,,,,,UnifiedBolt
toolName,String,ConfigurationToolInfoMetric,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,22,method_parameter,True,,,,,,,,UnifiedBolt
toolName,,ConfigurationToolInfoMetric,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,23,method_local,False,toolName,,,,,,,UnifiedBolt
manualData,,ConfigurationToolInfoMetric,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,24,method_local,False,false,,,,,,,UnifiedBolt
id,return,getId,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,32,method_local,False,,,,,,,,UnifiedBolt
id,String,setId,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,35,method_parameter,True,,,,,,,,UnifiedBolt
id,,setId,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,36,method_local,False,id,,,,,,,UnifiedBolt
toolName,return,getToolName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,43,method_local,False,,,,,,,,UnifiedBolt
toolName,String,setToolName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,50,method_parameter,True,,,,,,,,UnifiedBolt
toolName,,setToolName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,51,method_local,False,toolName,,,,,,,UnifiedBolt
url,return,getUrl,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,58,method_local,False,,,,,,,,UnifiedBolt
url,String,setUrl,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,65,method_parameter,True,,,,,,,,UnifiedBolt
url,,setUrl,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,66,method_local,False,url,,,,,,,UnifiedBolt
userName,return,getUserName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,73,method_local,False,,,,,,,,UnifiedBolt
userName,String,setUserName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,80,method_parameter,True,,,,,,,,UnifiedBolt
userName,,setUserName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,81,method_local,False,userName,,,,,,,UnifiedBolt
password,return,getPassword,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,88,method_local,False,,,,,,,,UnifiedBolt
password,String,setPassword,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,95,method_parameter,True,,,,,,,,UnifiedBolt
password,,setPassword,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,96,method_local,False,password,,,,,,,UnifiedBolt
toolType,return,getToolType,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,100,method_local,False,,,,,,,,UnifiedBolt
toolType,String,setToolType,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,103,method_parameter,True,,,,,,,,UnifiedBolt
toolType,,setToolType,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,104,method_local,False,toolType,,,,,,,UnifiedBolt
widgetName,return,getWidgetName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,108,method_local,False,,,,,,,,UnifiedBolt
widgetName,String,setWidgetName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,111,method_parameter,True,,,,,,,,UnifiedBolt
widgetName,,setWidgetName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,112,method_local,False,widgetName,,,,,,,UnifiedBolt
jobName,return,getJobName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,116,method_local,False,,,,,,,,UnifiedBolt
jobName,String,setJobName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,119,method_parameter,True,,,,,,,,UnifiedBolt
jobName,,setJobName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,120,method_local,False,jobName,,,,,,,UnifiedBolt
projectCode,return,getProjectCode,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,124,method_local,False,,,,,,,,UnifiedBolt
projectCode,String,setProjectCode,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,127,method_parameter,True,,,,,,,,UnifiedBolt
projectCode,,setProjectCode,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,128,method_local,False,projectCode,,,,,,,UnifiedBolt
selected,return,getSelected,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,132,method_local,False,,,,,,,,UnifiedBolt
selected,String,setSelected,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,135,method_parameter,True,,,,,,,,UnifiedBolt
selected,,setSelected,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,136,method_local,False,selected,,,,,,,UnifiedBolt
domain,return,getDomain,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,140,method_local,False,,,,,,,,UnifiedBolt
domain,String,setDomain,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,143,method_parameter,True,,,,,,,,UnifiedBolt
domain,,setDomain,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,144,method_local,False,domain,,,,,,,UnifiedBolt
host,return,getHost,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,148,method_local,False,,,,,,,,UnifiedBolt
host,String,setHost,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,151,method_parameter,True,,,,,,,,UnifiedBolt
host,,setHost,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,152,method_local,False,host,,,,,,,UnifiedBolt
port,return,getPort,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,156,method_local,False,,,,,,,,UnifiedBolt
port,String,setPort,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,159,method_parameter,True,,,,,,,,UnifiedBolt
port,,setPort,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,160,method_local,False,port,,,,,,,UnifiedBolt
dbType,return,getDbType,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,164,method_local,False,,,,,,,,UnifiedBolt
dbType,String,setDbType,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,167,method_parameter,True,,,,,,,,UnifiedBolt
dbType,,setDbType,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,168,method_local,False,dbType,,,,,,,UnifiedBolt
schema,return,getSchema,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,172,method_local,False,,,,,,,,UnifiedBolt
schema,String,setSchema,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,175,method_parameter,True,,,,,,,,UnifiedBolt
schema,,setSchema,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,176,method_local,False,schema,,,,,,,UnifiedBolt
repoName,return,getRepoName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,180,method_local,False,,,,,,,,UnifiedBolt
repoName,String,setRepoName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,183,method_parameter,True,,,,,,,,UnifiedBolt
repoName,,setRepoName,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,184,method_local,False,repoName,,,,,,,UnifiedBolt
secret,return,getSecret,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,188,method_local,False,,,,,,,,UnifiedBolt
secret,String,setSecret,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,191,method_parameter,True,,,,,,,,UnifiedBolt
secret,,setSecret,ConfigurationToolInfoMetric,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,192,method_local,False,secret,,,,,,,UnifiedBolt
fields,,,,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java,29,global,False,"""{jobList:0}"")",,,,,,,UnifiedBolt
fields,,,,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java,33,global,False,"""{jobList:0}"")",,,,,,,UnifiedBolt
applicationContext,AnnotationConfigApplicationContext,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,32,method_local,False,null,,,,,,,UnifiedBolt
applicationContext,,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,32,method_local,False,null,,,,,,,UnifiedBolt
repo,BuildToolRep,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,33,method_local,False,null,,,,,,,UnifiedBolt
repo,,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,33,method_local,False,null,,,,,,,UnifiedBolt
githubActionMetrics,GithubAction,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,34,method_local,False,null,,,,,,,UnifiedBolt
githubActionMetrics,,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,34,method_local,False,null,,,,,,,UnifiedBolt
result,String,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,35,method_local,False,"""SUCCESS""",,,,,,,UnifiedBolt
result,,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,35,method_local,False,"""SUCCESS""",,,,,,,UnifiedBolt
buildType,String,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,36,method_local,False,"""Github Action""",,,,,,,UnifiedBolt
buildType,,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,36,method_local,False,"""Github Action""",,,,,,,UnifiedBolt
pageLimit,int,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,37,method_local,False,100,,,,,,,UnifiedBolt
pageLimit,,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,37,method_local,False,100,,,,,,,UnifiedBolt
configurationRepo,ConfigurationSettingRep,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,38,method_local,False,null,,,,,,,UnifiedBolt
configurationRepo,,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,38,method_local,False,null,,,,,,,UnifiedBolt
configuration,ConfigurationSetting,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,39,method_local,False,null,,,,,,,UnifiedBolt
configuration,,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,39,method_local,False,null,,,,,,,UnifiedBolt
metric,,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,40,method_local,False,null,,,,,,,UnifiedBolt
metric1,ConfigurationToolInfoMetric,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,41,method_local,False,null,,,,,,,UnifiedBolt
metric1,,getLogger,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,41,method_local,False,null,,,,,,,UnifiedBolt
projectName,String,githubActionMain,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,47,method_parameter,True,,,,,,,,UnifiedBolt
Actions,"""Github",info,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,49,method_parameter,True,,,,,,,,UnifiedBolt
instanceURL,String,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,52,method_local,False,"""""",,,,,,,UnifiedBolt
instanceURL,,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,52,method_local,False,"""""",,,,,,,UnifiedBolt
branch,String,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,53,method_local,False,"""""",,,,,,,UnifiedBolt
branch,,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,53,method_local,False,"""""",,,,,,,UnifiedBolt
username,String,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,54,method_local,False,"""""",,,,,,,UnifiedBolt
username,,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,54,method_local,False,"""""",,,,,,,UnifiedBolt
password,String,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,55,method_local,False,null,,,,,,,UnifiedBolt
password,,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,55,method_local,False,null,,,,,,,UnifiedBolt
repoName,String,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,56,method_local,False,null,,,,,,,UnifiedBolt
repoName,,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,56,method_local,False,null,,,,,,,UnifiedBolt
firstRun,boolean,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,57,method_local,False,false,,,,,,,UnifiedBolt
firstRun,,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,57,method_local,False,false,,,,,,,UnifiedBolt
apiToken,String,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,58,method_local,False,null,,,,,,,UnifiedBolt
apiToken,,getBean,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,58,method_local,False,null,,,,,,,UnifiedBolt
name,"""Project",info,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,64,method_parameter,True,,,,,,,,UnifiedBolt
splitUrl,,GithubActionImplementation,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,68,method_local,False,null,,,,,,,UnifiedBolt
splitRepoName,,GithubActionImplementation,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,69,method_local,False,null,,,,,,,UnifiedBolt
metric1,,next,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,74,method_local,False,(ConfigurationToolInfoMetric) configuration1,,,,,,,UnifiedBolt
"Action"".equals(metric1.getToolName(","""Github",if,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,76,method_parameter,True,,,,,,,,UnifiedBolt
name,"""Tool",info,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,77,method_parameter,True,,,,,,,,UnifiedBolt
repoName,,if,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,88,method_local,False,projectName,,,,,,,UnifiedBolt
splitUrl,,split,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,93,method_local,False,new String[1],,,,,,,UnifiedBolt
splitRepoName,,split,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,95,method_local,False,new String[1],,,,,,,UnifiedBolt
result,,catch,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,107,method_local,False,"""FAIL""",,,,,,,UnifiedBolt
Date(,new,getLastRun,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,108,method_parameter,True,,,,,,,,UnifiedBolt
Date(,new,getLastRun,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,113,method_parameter,True,,,,,,,,UnifiedBolt
Action,"""GitHub",info,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,115,method_parameter,True,,,,,,,,UnifiedBolt
repo,,cleanObject,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,120,method_local,False,null,,,,,,,UnifiedBolt
githubActionMetrics,,cleanObject,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,121,method_local,False,null,,,,,,,UnifiedBolt
response,ResponseEntity<String>,parseAsArray,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,124,method_parameter,True,,,,,,,,UnifiedBolt
url,String,makeRestCall,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,128,method_parameter,True,,,,,,,,UnifiedBolt
response,return,exchange,GithubActionApplication,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java,136,method_local,False,,,,,,,,UnifiedBolt
SEGMENT_API,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,42,method_local,False,"""/api/v3/repos""",,,,,,arithmetic,UnifiedBolt
SEGMENT_API,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,42,method_local,False,"""/api/v3/repos""",,,,,,arithmetic,UnifiedBolt
PUBLIC_GITHUB_REPO_HOST,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,43,method_local,False,"""api.github.com""",,,,,,,UnifiedBolt
PUBLIC_GITHUB_REPO_HOST,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,43,method_local,False,"""api.github.com""",,,,,,,UnifiedBolt
PUBLIC_GITHUB_HOST_NAME,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,44,method_local,False,"""github.move.com""",,,,,,,UnifiedBolt
PUBLIC_GITHUB_HOST_NAME,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,44,method_local,False,"""github.move.com""",,,,,,,UnifiedBolt
ctx,AnnotationConfigApplicationContext,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,45,method_local,False,null,,,,,,,UnifiedBolt
ctx,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,45,method_local,False,null,,,,,,,UnifiedBolt
projectName,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,46,method_local,False,"""""",,,,,,,UnifiedBolt
projectName,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,46,method_local,False,"""""",,,,,,,UnifiedBolt
userName,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,47,method_local,False,"""""",,,,,,,UnifiedBolt
userName,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,47,method_local,False,"""""",,,,,,,UnifiedBolt
password,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,48,method_local,False,"""""",,,,,,,UnifiedBolt
password,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,48,method_local,False,"""""",,,,,,,UnifiedBolt
time,long,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,49,method_local,False,0,,,,,,,UnifiedBolt
time,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,49,method_local,False,0,,,,,,,UnifiedBolt
buildRepo,BuildToolRep,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,50,method_local,False,null,,,,,,,UnifiedBolt
buildRepo,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,50,method_local,False,null,,,,,,,UnifiedBolt
jobCollection,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,51,method_local,False,new ArrayList<>(),,,,,,,UnifiedBolt
pipelineUrl,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,52,method_local,False,null,,,,,,,UnifiedBolt
pipelineUrl,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,52,method_local,False,null,,,,,,,UnifiedBolt
singlePipelineUrl,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,53,method_local,False,null,,,,,,,UnifiedBolt
singlePipelineUrl,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,53,method_local,False,null,,,,,,,UnifiedBolt
githubUrl,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,54,method_local,False,null,,,,,,,UnifiedBolt
githubUrl,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,54,method_local,False,null,,,,,,,UnifiedBolt
jobsUrl,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,55,method_local,False,null,,,,,,,UnifiedBolt
jobsUrl,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,55,method_local,False,null,,,,,,,UnifiedBolt
size,int,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,56,method_local,False,0,,,,,,,UnifiedBolt
size,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,56,method_local,False,0,,,,,,,UnifiedBolt
lastPage,int,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,57,method_local,False,0,,,,,,,UnifiedBolt
lastPage,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,57,method_local,False,0,,,,,,,UnifiedBolt
lastBuildId,long,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,58,method_local,False,,,,,,,,UnifiedBolt
newbuildPipelineTimestamp,long,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,59,method_local,False,,,,,,,,UnifiedBolt
timestamp,long,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,60,method_local,False,,,,,,,,UnifiedBolt
page,int,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,61,method_local,False,1,,,,,,,UnifiedBolt
page,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,61,method_local,False,1,,,,,,,UnifiedBolt
per_page,int,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,62,method_local,False,100,,,,,,,UnifiedBolt
per_page,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,62,method_local,False,100,,,,,,,UnifiedBolt
totalPages,int,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,63,method_local,False,,,,,,,,UnifiedBolt
pipelineId,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,64,method_local,False,null,,,,,,,UnifiedBolt
pipelineId,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,64,method_local,False,null,,,,,,,UnifiedBolt
build,BuildTool,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,66,method_local,False,,,,,,,,UnifiedBolt
failurePatternRepo,BuildFailurePatternForProjectRepo,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,68,method_local,False,null,,,,,,,UnifiedBolt
failurePatternRepo,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,68,method_local,False,null,,,,,,,UnifiedBolt
repoName,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,69,method_local,False,,,,,,,,UnifiedBolt
githubUrl,String,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,74,method_local,False,baseUrl,,,,,,,UnifiedBolt
githubUrl,,getLogger,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,74,method_local,False,baseUrl,,,,,,,UnifiedBolt
URL,"""API",info,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,75,method_parameter,True,,,,,,,,UnifiedBolt
url,URL,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,80,class_field,False,null,,,,,,,UnifiedBolt
url,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,80,class_field,False,null,,,,,,,UnifiedBolt
hostName,String,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,81,class_field,False,"""""",,,,,,,UnifiedBolt
hostName,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,81,class_field,False,"""""",,,,,,,UnifiedBolt
protocol,String,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,82,class_field,False,"""""",,,,,,,UnifiedBolt
protocol,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,82,class_field,False,"""""",,,,,,,UnifiedBolt
port,int,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,83,class_field,False,,,,,,,,UnifiedBolt
hostUrl,String,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,93,class_field,False,"protocol + ""://"" + hostName + ""/""",,,,,,arithmetic,UnifiedBolt
hostUrl,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,93,class_field,False,"protocol + ""://"" + hostName + ""/""",,,,,,arithmetic,UnifiedBolt
githubUrl,,if,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,101,method_local,False,"protocol + ""://"" + hostName + ""/api/v3"" + ""/repos/""",,,,,,arithmetic,UnifiedBolt
githubUrl,,substring,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,104,method_local,False,"protocol + ""://"" + PUBLIC_GITHUB_REPO_HOST + ""/repos/""",,,,,,arithmetic,UnifiedBolt
URL:,"""API",debug,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,107,method_parameter,True,,,,,,,,UnifiedBolt
projectName,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,109,class_field,False,projectName,,,,,,,UnifiedBolt
buildRepo,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,110,class_field,False,repo,,,,,,,UnifiedBolt
repoName,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,111,class_field,False,repoName,,,,,,,UnifiedBolt
page,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,112,class_field,False,0,,,,,,,UnifiedBolt
tool,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,113,class_field,False,"repo.findByBuildTypeAndRepoNameAndNameOrderByTimestampDesc(""GITHUB"", repoName,",,,,,,,UnifiedBolt
delta,String,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,115,class_field,False,"""""",,,,,,,UnifiedBolt
delta,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,115,class_field,False,"""""",,,,,,,UnifiedBolt
timeString,String,if,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,117,method_local,False,"""""",,,,,,,UnifiedBolt
timeString,,if,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,117,method_local,False,"""""",,,,,,,UnifiedBolt
count,int,if,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,118,method_local,False,0,,,,,,,UnifiedBolt
count,,if,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,118,method_local,False,0,,,,,,,UnifiedBolt
delta,,if,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,124,method_local,False,"""&created=>"" + timeString",,,,,,arithmetic,UnifiedBolt
page,int,getInt,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,133,method_local,False,1,,,,,,,UnifiedBolt
page,,getInt,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,133,method_local,False,1,,,,,,,UnifiedBolt
runsCollected,int,getInt,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,134,method_local,False,0,,,,,,,UnifiedBolt
runsCollected,,getInt,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,134,method_local,False,0,,,,,,,UnifiedBolt
pipelineUrl,,while,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,139,method_local,False,"githubUrl + ""/actions/runs?"" + ""page="" + page + ""&per_page="" + per_page + delta",,,,,,arithmetic,UnifiedBolt
builds,,get,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,147,method_local,False,"processPipelineData(valueArray, user, pass, projectName, repoName,",,,,,,,UnifiedBolt
Saved:,"""Builds",info,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,152,method_parameter,True,,,,,,,,UnifiedBolt
builds,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,165,class_field,False,new ArrayList<>(),,,,,,,UnifiedBolt
pipelineId,,setRepoName,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,176,method_local,False,""""" + id",,,,,,arithmetic,UnifiedBolt
jobsUrl,,setRepoName,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,179,method_local,False,"githubUrl + ""/actions/runs/"" + this.pipelineId + ""/jobs""",,,,,,arithmetic,UnifiedBolt
temp_date,String,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,195,class_field,False,null,,,,,,,UnifiedBolt
temp_date,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,195,class_field,False,null,,,,,,,UnifiedBolt
temp_date1,String,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,196,class_field,False,null,,,,,,,UnifiedBolt
temp_date1,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,196,class_field,False,null,,,,,,,UnifiedBolt
createTime,long,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,200,class_field,False,0,,,,,,,UnifiedBolt
createTime,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,200,class_field,False,0,,,,,,,UnifiedBolt
updatedTime,long,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,208,class_field,False,0,,,,,,,UnifiedBolt
updatedTime,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,208,class_field,False,0,,,,,,,UnifiedBolt
temp_date,,setTimestamp,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,213,method_local,False,null,,,,,,,UnifiedBolt
completeTime,long,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,217,class_field,False,0,,,,,,,UnifiedBolt
completeTime,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,217,class_field,False,0,,,,,,,UnifiedBolt
duration,long,BuildToolMetric,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,224,method_local,False,0,,,,,,,UnifiedBolt
duration,,BuildToolMetric,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,224,method_local,False,0,,,,,,,UnifiedBolt
duration,,BuildToolMetric,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,226,method_local,False,updatedTime - createTime,,,,,,arithmetic,UnifiedBolt
jobsList,,setTriggerType,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,244,method_local,False,new ArrayList<>(),,,,,,,UnifiedBolt
completeDate,String,getJSONObject,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,249,method_local,False,null,,,,,,,UnifiedBolt
completeDate,,getJSONObject,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,249,method_local,False,null,,,,,,,UnifiedBolt
completed,long,getJSONObject,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,250,method_local,False,0,,,,,,,UnifiedBolt
completed,,getJSONObject,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,250,method_local,False,0,,,,,,,UnifiedBolt
started,long,getJSONObject,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,251,method_local,False,0,,,,,,,UnifiedBolt
started,,getJSONObject,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,251,method_local,False,0,,,,,,,UnifiedBolt
tempDate,long,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,255,class_field,False,0,,,,,,,UnifiedBolt
tempDate,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,255,class_field,False,0,,,,,,,UnifiedBolt
completed,,getTimeInMiliseconds,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,259,method_local,False,tempDate,,,,,,,UnifiedBolt
completeDate,,setCompletedTime,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,262,method_local,False,null,,,,,,,UnifiedBolt
started,,getTimeInMiliseconds,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,268,method_local,False,tempDate,,,,,,,UnifiedBolt
"esception""","""parsing",error,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,281,method_parameter,True,,,,,,,,UnifiedBolt
builds,return,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,294,class_field,False,,,,,,,,UnifiedBolt
githubUrl,String,processFailure,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,297,method_parameter,True,,,,,,,,UnifiedBolt
user,String,processFailure,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,297,method_parameter,True,,,,,,,,UnifiedBolt
pass,String,processFailure,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,297,method_parameter,True,,,,,,,,UnifiedBolt
id,long,processFailure,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,297,method_parameter,True,,,,,,,,UnifiedBolt
failurePattern,,getBean,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,301,method_local,False,failurePatternRepo,,,,,,,UnifiedBolt
newList,,getPatternMetrics,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,307,method_local,False,new ArrayList<>(),,,,,,,UnifiedBolt
url,String,getPatternMetrics,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,308,method_local,False,"githubUrl + ""/actions/jobs/"" + id + ""/logs""",,,,,,arithmetic,UnifiedBolt
url,,getPatternMetrics,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,308,method_local,False,"githubUrl + ""/actions/jobs/"" + id + ""/logs""",,,,,,arithmetic,UnifiedBolt
flag,boolean,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,318,class_field,False,true,,,,,,,UnifiedBolt
flag,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,318,class_field,False,true,,,,,,,UnifiedBolt
flag,,,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,326,class_field,False,false,,,,,,,UnifiedBolt
Exception/,"""ProcessFailure",error,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,357,method_parameter,True,,,,,,,,UnifiedBolt
temp_date,String,getTimeInMiliseconds,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,362,method_parameter,True,,,,,,,,UnifiedBolt
tempTime,String,split,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,367,method_local,False,"splitDate[0] + "" "" + temp[0]",,,,,,arithmetic,UnifiedBolt
tempTime,,split,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,367,method_local,False,"splitDate[0] + "" "" + temp[0]",,,,,,arithmetic,UnifiedBolt
"HH:mm:ss""","""yyyy-MM-dd",forPattern,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,369,method_parameter,True,,,,,,,,UnifiedBolt
url,String,makeRestCall,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,374,method_parameter,True,,,,,,,,UnifiedBolt
userId,String,makeRestCall,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,374,method_parameter,True,,,,,,,,UnifiedBolt
password,String,makeRestCall,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,374,method_parameter,True,,,,,,,,UnifiedBolt
String,final,createHeaders,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,391,method_parameter,True,,,,,,,,UnifiedBolt
String,final,createHeaders,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,391,method_parameter,True,,,,,,,,UnifiedBolt
auth,String,createHeaders,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,392,method_local,False,"userId + "":"" + password",,,,,,arithmetic,UnifiedBolt
auth,,createHeaders,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,392,method_local,False,"userId + "":"" + password",,,,,,arithmetic,UnifiedBolt
headers,return,set,GithubActionImplementation,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java,398,method_local,False,,,,,,,,UnifiedBolt
